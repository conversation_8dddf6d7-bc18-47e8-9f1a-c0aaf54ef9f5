{"version": 3, "sources": ["webpack:///./src/views/Borrowing.vue", "webpack:///./src/views/Borrowing.vue?2921", "webpack:///./src/views/Borrowing.vue?fd62"], "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_createVNode", "_component_el_button", "type", "onClick", "_cache", "$event", "$setup", "dialogVisible", "_component_el_input", "searchQuery", "placeholder", "clearable", "prefix", "_withCtx", "_component_el_icon", "_component_search", "_component_el_table", "data", "filteredBorrowings", "style", "border", "_component_el_table_column", "prop", "label", "width", "default", "scope", "getBookTitle", "row", "bookId", "getUserName", "userId", "returnDate", "_component_el_tag", "_createBlock", "size", "returnBook", "id", "_component_el_dialog", "title", "footer", "_hoisted_3", "saveBorrowing", "_component_el_form", "model", "borrowingForm", "label-width", "_component_el_form_item", "_component_el_select", "_Fragment", "_renderList", "availableBooks", "book", "_component_el_option", "key", "value", "users", "user", "name", "_component_el_date_picker", "borrowDate", "format", "value-format", "components", "Search", "setup", "store", "useStore", "ref", "Date", "toISOString", "split", "books", "computed", "state", "borrowings", "filter", "stock", "borrowed", "query", "toLowerCase", "borrowing", "includes", "find", "b", "u", "ElMessage", "warning", "success", "dispatch", "error", "borrowingId", "__exports__", "render"], "mappings": "uKACOA,MAAM,uB,GAGJA,MAAM,c,GA0FDA,MAAM,iB,yoBA7FlBC,gCAmGM,MAnGNC,EAmGM,C,cAlGJC,gCAAa,UAAT,QAAI,IAERA,gCAYM,MAZNC,EAYM,CAXJC,yBAAwEC,EAAA,CAA7DC,KAAK,UAAWC,QAAKC,EAAA,KAAAA,EAAA,GAAAC,GAAEC,EAAAC,eAAgB,I,8BAAM,IAAIH,EAAA,KAAAA,EAAA,I,6BAAJ,W,aACxDJ,yBASWQ,EAAA,C,WARAF,EAAAG,Y,qCAAAH,EAAAG,YAAWJ,GACpBK,YAAY,SACZf,MAAM,eACNgB,UAAA,I,CAEWC,OAAMC,qBACf,IAA6B,CAA7Bb,yBAA6Bc,EAAA,M,6BAApB,IAAU,CAAVd,yBAAUe,K,iCAKzBf,yBAmCWgB,EAAA,CAnCAC,KAAMX,EAAAY,mBAAoBC,MAAA,eAAoBC,OAAA,I,8BACvD,IAAmD,CAAnDpB,yBAAmDqB,EAAA,CAAlCC,KAAK,KAAKC,MAAM,KAAKC,MAAM,OAC5CxB,yBAIkBqB,EAAA,CAJDE,MAAM,MAAI,CACdE,QAAOZ,qBAAEa,GAAK,C,0DACpBpB,EAAAqB,aAAaD,EAAME,IAAIC,SAAM,K,MAGpC7B,yBAIkBqB,EAAA,CAJDE,MAAM,OAAK,CACfE,QAAOZ,qBAAEa,GAAK,C,0DACpBpB,EAAAwB,YAAYJ,EAAME,IAAIG,SAAM,K,MAGnC/B,yBAAkDqB,EAAA,CAAjCC,KAAK,aAAaC,MAAM,SACzCvB,yBAIkBqB,EAAA,CAJDC,KAAK,aAAaC,MAAM,Q,CAC5BE,QAAOZ,qBAAEa,GAAK,C,0DACpBA,EAAME,IAAII,YAAc,OAAJ,K,MAG3BhC,yBAMkBqB,EAAA,CANDE,MAAM,MAAI,CACdE,QAAOZ,qBAAEa,GAAK,CACvB1B,yBAESiC,EAAA,CAFA/B,KAAMwB,EAAME,IAAII,WAAa,UAAY,W,8BAChD,IAA0C,C,0DAAvCN,EAAME,IAAII,WAAa,MAAQ,OAAX,K,4BAI7BhC,yBASkBqB,EAAA,CATDE,MAAM,KAAKC,MAAM,O,CACrBC,QAAOZ,qBAAEa,GAAK,CAEdA,EAAME,IAAII,W,iEADnBE,yBAKejC,EAAA,C,MAHbkC,KAAK,QACLjC,KAAK,UACJC,QAAKE,GAAEC,EAAA8B,WAAWV,EAAME,IAAIS,K,8BAC9B,IAAEjC,EAAA,KAAAA,EAAA,I,6BAAF,S,0DAMPJ,yBA2CYsC,EAAA,C,WA1CDhC,EAAAC,c,qCAAAD,EAAAC,cAAaF,GACtBkC,MAAM,OACNf,MAAM,O,CAkCKgB,OAAM3B,qBACf,IAGO,CAHPf,gCAGO,OAHP2C,EAGO,CAFLzC,yBAAwDC,EAAA,CAA5CE,QAAKC,EAAA,KAAAA,EAAA,GAAAC,GAAEC,EAAAC,eAAgB,I,8BAAO,IAAEH,EAAA,KAAAA,EAAA,I,6BAAF,S,aAC1CJ,yBAA+DC,EAAA,CAApDC,KAAK,UAAWC,QAAOG,EAAAoC,e,8BAAe,IAAEtC,EAAA,MAAAA,EAAA,K,6BAAF,S,6DAnCrD,IA+BU,CA/BVJ,yBA+BU2C,EAAA,CA/BAC,MAAOtC,EAAAuC,cAAeC,cAAY,S,8BAC1C,IASe,CATf9C,yBASe+C,EAAA,CATDxB,MAAM,MAAI,C,6BACtB,IAOY,CAPZvB,yBAOYgD,EAAA,C,WAPQ1C,EAAAuC,cAAchB,O,qCAAdvB,EAAAuC,cAAchB,OAAMxB,GAAEK,YAAY,OAAOS,MAAA,gB,8BAEzD,IAA8B,E,2BADhCvB,gCAKEqD,cAAA,KAAAC,wBAJe5C,EAAA6C,eAARC,I,yBADTlB,yBAKEmB,EAAA,CAHCC,IAAKF,EAAKf,GACVd,MAAO6B,EAAKb,MACZgB,MAAOH,EAAKf,I,mEAInBrC,yBASe+C,EAAA,CATDxB,MAAM,OAAK,C,6BACvB,IAOY,CAPZvB,yBAOYgD,EAAA,C,WAPQ1C,EAAAuC,cAAcd,O,qCAAdzB,EAAAuC,cAAcd,OAAM1B,GAAEK,YAAY,QAAQS,MAAA,gB,8BAE1D,IAAqB,E,2BADvBvB,gCAKEqD,cAAA,KAAAC,wBAJe5C,EAAAkD,MAARC,I,yBADTvB,yBAKEmB,EAAA,CAHCC,IAAKG,EAAKpB,GACVd,MAAOkC,EAAKC,KACZH,MAAOE,EAAKpB,I,mEAInBrC,yBASe+C,EAAA,CATDxB,MAAM,QAAM,C,6BACxB,IAOE,CAPFvB,yBAOE2D,EAAA,C,WANSrD,EAAAuC,cAAce,W,qCAAdtD,EAAAuC,cAAce,WAAUvD,GACjCH,KAAK,OACLQ,YAAY,OACZS,MAAA,eACA0C,OAAO,aACPC,eAAa,c,oJAoBV,GACbJ,KAAM,gBACNK,WAAY,CACVC,oBAEFC,QACE,MAAMC,EAAQC,iBACR5D,EAAgB6D,kBAAI,GACpB3D,EAAc2D,iBAAI,IAElBvB,EAAgBuB,iBAAI,CACxBvC,OAAQ,KACRE,OAAQ,KACR6B,YAAY,IAAIS,MAAOC,cAAcC,MAAM,KAAK,KAG5CC,EAAQC,sBAAS,IAAMP,EAAMQ,MAAMF,OACnChB,EAAQiB,sBAAS,IAAMP,EAAMQ,MAAMlB,OACnCmB,EAAaF,sBAAS,IAAMP,EAAMQ,MAAMC,YAExCxB,EAAiBsB,sBAAS,IACvBD,EAAMjB,MAAMqB,OAAOxB,GAAQA,EAAKyB,MAAQzB,EAAK0B,WAGhD5D,EAAqBuD,sBAAS,KAClC,IAAKhE,EAAY8C,MAAO,OAAOoB,EAAWpB,MAE1C,MAAMwB,EAAQtE,EAAY8C,MAAMyB,cAChC,OAAOL,EAAWpB,MAAMqB,OAAOK,IAC7B,MAAM7B,EAAOzB,EAAasD,EAAUpD,QAAQmD,cACtCvB,EAAO3B,EAAYmD,EAAUlD,QAAQiD,cAC3C,OAAO5B,EAAK8B,SAASH,IAAUtB,EAAKyB,SAASH,OAI3CpD,EAAgBE,IACpB,MAAMuB,EAAOoB,EAAMjB,MAAM4B,KAAKC,GAAKA,EAAE/C,KAAOR,GAC5C,OAAOuB,EAAOA,EAAKb,MAAQ,QAGvBT,EAAeC,IACnB,MAAM0B,EAAOD,EAAMD,MAAM4B,KAAKE,GAAKA,EAAEhD,KAAON,GAC5C,OAAO0B,EAAOA,EAAKC,KAAO,QAGtBhB,EAAgBA,KACpB,IAAKG,EAAcU,MAAM1B,SAAWgB,EAAcU,MAAMxB,OAEtD,YADAuD,OAAUC,QAAQ,aAIpB,MAAMC,EAAUtB,EAAMuB,SAAS,aAAc,CAC3C5D,OAAQgB,EAAcU,MAAM1B,OAC5BE,OAAQc,EAAcU,MAAMxB,SAG1ByD,GACFF,OAAUE,QAAQ,QAClBjF,EAAcgD,OAAQ,EACtBV,EAAcU,MAAQ,CACpB1B,OAAQ,KACRE,OAAQ,KACR6B,YAAY,IAAIS,MAAOC,cAAcC,MAAM,KAAK,KAGlDe,OAAUI,MAAM,gBAIdtD,EAAcuD,IAClBzB,EAAMuB,SAAS,aAAcE,GAC7BL,OAAUE,QAAQ,WAGpB,MAAO,CACLjF,gBACAsC,gBACA2B,QACAhB,QACAmB,aACAxB,iBACAjC,qBACAT,cACAkB,eACAG,cACAY,gBACAN,gB,iCC5LN,MAAMwD,EAA2B,IAAgB,EAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAE1E,gB,kCCTf", "file": "js/chunk-a1ab17f4.f89343c4.js", "sourcesContent": ["<template>\n  <div class=\"borrowing-container\">\n    <h1>借阅管理</h1>\n    \n    <div class=\"action-bar\">\n      <el-button type=\"primary\" @click=\"dialogVisible = true\">新增借阅</el-button>\n      <el-input\n        v-model=\"searchQuery\"\n        placeholder=\"搜索借阅记录\"\n        class=\"search-input\"\n        clearable\n      >\n        <template #prefix>\n          <el-icon><search /></el-icon>\n        </template>\n      </el-input>\n    </div>\n    \n    <el-table :data=\"filteredBorrowings\" style=\"width: 100%\" border>\n      <el-table-column prop=\"id\" label=\"ID\" width=\"80\" />\n      <el-table-column label=\"图书\">\n        <template #default=\"scope\">\n          {{ getBookTitle(scope.row.bookId) }}\n        </template>\n      </el-table-column>\n      <el-table-column label=\"借阅人\">\n        <template #default=\"scope\">\n          {{ getUserName(scope.row.userId) }}\n        </template>\n      </el-table-column>\n      <el-table-column prop=\"borrowDate\" label=\"借阅日期\" />\n      <el-table-column prop=\"returnDate\" label=\"归还日期\">\n        <template #default=\"scope\">\n          {{ scope.row.returnDate || '未归还' }}\n        </template>\n      </el-table-column>\n      <el-table-column label=\"状态\">\n        <template #default=\"scope\">\n          <el-tag :type=\"scope.row.returnDate ? 'success' : 'warning'\">\n            {{ scope.row.returnDate ? '已归还' : '借阅中' }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" width=\"120\">\n        <template #default=\"scope\">\n          <el-button\n            v-if=\"!scope.row.returnDate\"\n            size=\"small\"\n            type=\"success\"\n            @click=\"returnBook(scope.row.id)\"\n          >归还</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    \n    <!-- 新增借阅对话框 -->\n    <el-dialog\n      v-model=\"dialogVisible\"\n      title=\"新增借阅\"\n      width=\"50%\"\n    >\n      <el-form :model=\"borrowingForm\" label-width=\"120px\">\n        <el-form-item label=\"图书\">\n          <el-select v-model=\"borrowingForm.bookId\" placeholder=\"选择图书\" style=\"width: 100%\">\n            <el-option\n              v-for=\"book in availableBooks\"\n              :key=\"book.id\"\n              :label=\"book.title\"\n              :value=\"book.id\"\n            />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"借阅人\">\n          <el-select v-model=\"borrowingForm.userId\" placeholder=\"选择借阅人\" style=\"width: 100%\">\n            <el-option\n              v-for=\"user in users\"\n              :key=\"user.id\"\n              :label=\"user.name\"\n              :value=\"user.id\"\n            />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"借阅日期\">\n          <el-date-picker\n            v-model=\"borrowingForm.borrowDate\"\n            type=\"date\"\n            placeholder=\"选择日期\"\n            style=\"width: 100%\"\n            format=\"YYYY-MM-DD\"\n            value-format=\"YYYY-MM-DD\"\n          />\n        </el-form-item>\n      </el-form>\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"dialogVisible = false\">取消</el-button>\n          <el-button type=\"primary\" @click=\"saveBorrowing\">确定</el-button>\n        </span>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { ref, computed } from 'vue'\nimport { useStore } from 'vuex'\nimport { ElMessage } from 'element-plus'\nimport { Search } from '@element-plus/icons-vue'\n\nexport default {\n  name: 'BorrowingView',\n  components: {\n    Search\n  },\n  setup() {\n    const store = useStore()\n    const dialogVisible = ref(false)\n    const searchQuery = ref('')\n    \n    const borrowingForm = ref({\n      bookId: null,\n      userId: null,\n      borrowDate: new Date().toISOString().split('T')[0]\n    })\n    \n    const books = computed(() => store.state.books)\n    const users = computed(() => store.state.users)\n    const borrowings = computed(() => store.state.borrowings)\n    \n    const availableBooks = computed(() => {\n      return books.value.filter(book => book.stock > book.borrowed)\n    })\n    \n    const filteredBorrowings = computed(() => {\n      if (!searchQuery.value) return borrowings.value\n      \n      const query = searchQuery.value.toLowerCase()\n      return borrowings.value.filter(borrowing => {\n        const book = getBookTitle(borrowing.bookId).toLowerCase()\n        const user = getUserName(borrowing.userId).toLowerCase()\n        return book.includes(query) || user.includes(query)\n      })\n    })\n    \n    const getBookTitle = (bookId) => {\n      const book = books.value.find(b => b.id === bookId)\n      return book ? book.title : '未知图书'\n    }\n    \n    const getUserName = (userId) => {\n      const user = users.value.find(u => u.id === userId)\n      return user ? user.name : '未知用户'\n    }\n    \n    const saveBorrowing = () => {\n      if (!borrowingForm.value.bookId || !borrowingForm.value.userId) {\n        ElMessage.warning('请选择图书和借阅人')\n        return\n      }\n      \n      const success = store.dispatch('borrowBook', {\n        bookId: borrowingForm.value.bookId,\n        userId: borrowingForm.value.userId\n      })\n      \n      if (success) {\n        ElMessage.success('借阅成功')\n        dialogVisible.value = false\n        borrowingForm.value = {\n          bookId: null,\n          userId: null,\n          borrowDate: new Date().toISOString().split('T')[0]\n        }\n      } else {\n        ElMessage.error('借阅失败，可能库存不足')\n      }\n    }\n    \n    const returnBook = (borrowingId) => {\n      store.dispatch('returnBook', borrowingId)\n      ElMessage.success('图书归还成功')\n    }\n    \n    return {\n      dialogVisible,\n      borrowingForm,\n      books,\n      users,\n      borrowings,\n      availableBooks,\n      filteredBorrowings,\n      searchQuery,\n      getBookTitle,\n      getUserName,\n      saveBorrowing,\n      returnBook\n    }\n  }\n}\n</script>\n\n<style scoped>\n.borrowing-container {\n  padding: 20px;\n}\n\n.action-bar {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 20px;\n}\n\n.search-input {\n  width: 300px;\n}\n</style>\n", "import { render } from \"./Borrowing.vue?vue&type=template&id=49ce15ca&scoped=true\"\nimport script from \"./Borrowing.vue?vue&type=script&lang=js\"\nexport * from \"./Borrowing.vue?vue&type=script&lang=js\"\n\nimport \"./Borrowing.vue?vue&type=style&index=0&id=49ce15ca&scoped=true&lang=css\"\n\nimport exportComponent from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\软件架构大作业\\\\homework\\\\node_modules\\\\vue-loader-v16\\\\dist\\\\exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-49ce15ca\"]])\n\nexport default __exports__", "export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader-v16/dist/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader-v16/dist/index.js??ref--1-1!./Borrowing.vue?vue&type=style&index=0&id=49ce15ca&scoped=true&lang=css\""], "sourceRoot": ""}