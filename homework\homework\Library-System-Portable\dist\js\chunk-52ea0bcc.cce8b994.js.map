{"version": 3, "sources": ["webpack:///./src/components/HighchartsWrapper.vue", "webpack:///./src/components/HighchartsWrapper.vue?0049", "webpack:///./src/views/Statistics.vue?cdca", "webpack:///./node_modules/core-js/modules/es.iterator.map.js", "webpack:///./src/components/HighchartsWrapper.vue?d869", "webpack:///./src/views/Statistics.vue", "webpack:///./src/views/Statistics.vue?11c7"], "names": ["ref", "class", "_createElementBlock", "_hoisted_1", "name", "props", "options", "type", "Object", "required", "setup", "chartContainer", "chart", "createChart", "console", "log", "hasHighcharts", "window", "Highcharts", "<PERSON><PERSON><PERSON><PERSON>", "value", "optionsType", "optionsKeys", "keys", "innerHTML", "error", "message", "warn", "destroy<PERSON>hart", "destroy", "onMounted", "checkAndCreateChart", "retryCount", "setTimeout", "onUnmounted", "watch", "deep", "__exports__", "render", "$", "call", "aCallable", "anObject", "getIteratorDirect", "createIteratorProxy", "callWithSafeIterationClosing", "iteratorClose", "iteratorHelperWithoutClosingOnEarlyError", "IS_PURE", "mapWithoutClosingOnEarlyError", "TypeError", "IteratorProxy", "iterator", "this", "result", "next", "done", "mapper", "counter", "target", "proto", "real", "forced", "map", "_createElementVNode", "_createVNode", "_component_el_row", "gutter", "_component_el_col", "span", "_component_el_card", "header", "_withCtx", "_hoisted_2", "_component_el_button", "size", "onClick", "$setup", "exportCategoryChart", "_cache", "_hoisted_3", "_component_HighchartsWrapper", "categoryChartOptions", "_hoisted_4", "exportBorrowingChart", "_hoisted_5", "borrowingPieChartOptions", "_hoisted_6", "exportTrendChart", "_hoisted_7", "borrowingTrendChartOptions", "_hoisted_8", "exportStockChart", "_hoisted_9", "stockComparisonChartOptions", "components", "HighchartsWrapper", "store", "useStore", "categoryChartRef", "borrowingChartRef", "trendChartRef", "stockChartRef", "books", "computed", "state", "categoryData", "getters", "getBooksByCategory", "borrowingData", "getBorrowingStatistics", "stockData", "categories", "for<PERSON>ach", "book", "category", "stock", "borrowed", "entries", "data", "available", "maxCategoryV<PERSON>ue", "Math", "max", "values", "maxBorrowingValue", "maxStockValue", "length", "item", "getBaseOptions", "credits", "enabled", "exporting", "buttons", "contextButton", "menuItems", "responsive", "rules", "condition", "max<PERSON><PERSON><PERSON>", "chartOptions", "legend", "layout", "align", "verticalAlign", "config", "height", "title", "text", "subtitle", "xAxis", "yAxis", "min", "series", "colorByPoint", "tooltip", "pointFormat", "borrowingEntries", "pieData", "y", "style", "fontSize", "dataLabels", "format", "plotOptions", "pie", "allowPointSelect", "cursor", "showInLegend", "distance", "opposite", "ElMessage", "info"], "mappings": "2IACOA,IAAI,iBAAiBC,MAAM,wB,wDAAhCC,gCAA6D,MAA7DC,EAA6D,UAMhD,OACbC,KAAM,oBACNC,MAAO,CACLC,QAAS,CACPC,KAAMC,OACNC,UAAU,IAGdC,MAAML,GACJ,MAAMM,EAAiBX,iBAAI,MAC3B,IAAIY,EAAQ,KAEZ,MAAMC,EAAcA,KAQlB,GAPAC,QAAQC,IAAI,YAAa,CACvBC,gBAAiBC,OAAOC,WACxBC,eAAgBR,EAAeS,MAC/BC,mBAAoBhB,EAAMC,QAC1BgB,YAAajB,EAAMC,QAAUE,OAAOe,KAAKlB,EAAMC,SAAW,KAGxDW,OAAOC,YAAcP,EAAeS,OAASf,EAAMC,QACrD,IAEEK,EAAeS,MAAMI,UAAY,GAGjCZ,EAAQK,OAAOC,WAAWN,MAAMD,EAAeS,MAAOf,EAAMC,SAC5DQ,QAAQC,IAAI,UAAWH,GACvB,MAAOa,GACPX,QAAQW,MAAM,oBAAqBA,GACnCX,QAAQW,MAAM,QAASA,EAAMC,SAC7BZ,QAAQW,MAAM,QAASpB,EAAMC,SAGzBK,EAAeS,QACjBT,EAAeS,MAAMI,UAAY,mOAEkBC,EAAMC,0CAK7DZ,QAAQa,KAAK,4BAEThB,EAAeS,QACjBT,EAAeS,MAAMI,UAAY,yHAKjCI,EAAeA,KACfhB,IACFA,EAAMiB,UACNjB,EAAQ,OAqCZ,OAjCAkB,uBAAU,KACRhB,QAAQC,IAAI,6BAGZ,MAAMgB,EAAsBA,CAACC,EAAa,KACpCf,OAAOC,WACTL,IACSmB,EAAa,IACtBlB,QAAQC,IAAI,oBAAoBiB,EAAa,QAC7CC,WAAW,KACTF,EAAoBC,EAAa,IAChC,OAEHlB,QAAQW,MAAM,2BACVd,EAAeS,QACjBT,EAAeS,MAAMI,UAAY,kIAKvCO,MAGFG,yBAAY,KACVN,MAIFO,mBAAM,IAAM9B,EAAMC,QAAS,KACzBsB,IACAf,KACC,CAAEuB,MAAM,IAEJ,CACLzB,oB,iCC3FN,MAAM0B,EAA2B,IAAgB,EAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAE1E,U,6DCTf,W,kCCCA,IAAIC,EAAI,EAAQ,QACZC,EAAO,EAAQ,QACfC,EAAY,EAAQ,QACpBC,EAAW,EAAQ,QACnBC,EAAoB,EAAQ,QAC5BC,EAAsB,EAAQ,QAC9BC,EAA+B,EAAQ,QACvCC,EAAgB,EAAQ,QACxBC,EAA2C,EAAQ,QACnDC,EAAU,EAAQ,QAElBC,GAAiCD,GAAWD,EAAyC,MAAOG,WAE5FC,EAAgBP,GAAoB,WACtC,IAAIQ,EAAWC,KAAKD,SAChBE,EAASZ,EAASF,EAAKa,KAAKE,KAAMH,IAClCI,EAAOH,KAAKG,OAASF,EAAOE,KAChC,IAAKA,EAAM,OAAOX,EAA6BO,EAAUC,KAAKI,OAAQ,CAACH,EAAOlC,MAAOiC,KAAKK,YAAY,MAKxGnB,EAAE,CAAEoB,OAAQ,WAAYC,OAAO,EAAMC,MAAM,EAAMC,OAAQd,GAAWC,GAAiC,CACnGc,IAAK,SAAaN,GAChBf,EAASW,MACT,IACEZ,EAAUgB,GACV,MAAOhC,GACPqB,EAAcO,KAAM,QAAS5B,GAG/B,OAAIwB,EAAsCT,EAAKS,EAA+BI,KAAMI,GAE7E,IAAIN,EAAcR,EAAkBU,MAAO,CAChDI,OAAQA,Q,kCCnCd,W,yFCCOxD,MAAM,wB,GAWIA,MAAM,e,GAKRA,MAAM,mB,GAgBJA,MAAM,e,GAKRA,MAAM,mB,GAYJA,MAAM,e,GAKRA,MAAM,mB,GAgBJA,MAAM,e,GAKRA,MAAM,mB,+RA3EnBC,gCAqFM,MArFNC,EAqFM,C,YApFJ6D,gCAAa,UAAT,QAAI,I,YACRA,gCAEI,KAFD/D,MAAM,oBAAmB,uDAE5B,IAGAgE,yBAkBSC,EAAA,CAlBAC,OAAQ,IAAE,C,6BACjB,IAgBS,CAhBTF,yBAgBSG,EAAA,CAhBAC,KAAM,IAAE,C,6BACf,IAcU,CAdVJ,yBAcUK,EAAA,CAdDrE,MAAM,YAAU,CACZsE,OAAMC,qBACf,IAGM,CAHNR,gCAGM,MAHNS,EAGM,C,YAFJT,gCAAmC,YAA7B,0BAAsB,IAC5BC,yBAAqES,EAAA,CAA1DC,KAAK,QAASC,QAAOC,EAAAC,qB,8BAAqB,IAAIC,EAAA,KAAAA,EAAA,I,6BAAJ,W,4DAGzD,IAMM,CANNf,gCAMM,MANNgB,EAMM,CALJf,yBAIEgB,EAAA,CAHAjF,IAAI,mBACHM,QAASuE,EAAAK,qBACVjF,MAAM,S,8CAQhBgE,yBAmCSC,EAAA,CAnCAC,OAAQ,GAAIlE,MAAM,a,8BACzB,IAgBS,CAhBTgE,yBAgBSG,EAAA,CAhBAC,KAAM,IAAE,C,6BACf,IAcU,CAdVJ,yBAcUK,EAAA,CAdDrE,MAAM,YAAU,CACZsE,OAAMC,qBACf,IAGM,CAHNR,gCAGM,MAHNmB,EAGM,C,YAFJnB,gCAAgC,YAA1B,uBAAmB,IACzBC,yBAAsES,EAAA,CAA3DC,KAAK,QAASC,QAAOC,EAAAO,sB,8BAAsB,IAAIL,EAAA,KAAAA,EAAA,I,6BAAJ,W,4DAG1D,IAMM,CANNf,gCAMM,MANNqB,EAMM,CALJpB,yBAIEgB,EAAA,CAHAjF,IAAI,oBACHM,QAASuE,EAAAS,yBACVrF,MAAM,S,sCAKdgE,yBAgBSG,EAAA,CAhBAC,KAAM,IAAE,C,6BACf,IAcU,CAdVJ,yBAcUK,EAAA,CAdDrE,MAAM,YAAU,CACZsE,OAAMC,qBACf,IAGM,CAHNR,gCAGM,MAHNuB,EAGM,C,YAFJvB,gCAAiC,YAA3B,wBAAoB,IAC1BC,yBAAkES,EAAA,CAAvDC,KAAK,QAASC,QAAOC,EAAAW,kB,8BAAkB,IAAIT,EAAA,KAAAA,EAAA,I,6BAAJ,W,4DAGtD,IAMM,CANNf,gCAMM,MANNyB,EAMM,CALJxB,yBAIEgB,EAAA,CAHAjF,IAAI,gBACHM,QAASuE,EAAAa,2BACVzF,MAAM,S,8CAQhBgE,yBAkBSC,EAAA,CAlBAC,OAAQ,GAAIlE,MAAM,a,8BACzB,IAgBS,CAhBTgE,yBAgBSG,EAAA,CAhBAC,KAAM,IAAE,C,6BACf,IAcU,CAdVJ,yBAcUK,EAAA,CAdDrE,MAAM,YAAU,CACZsE,OAAMC,qBACf,IAGM,CAHNR,gCAGM,MAHN2B,EAGM,C,YAFJ3B,gCAAqC,YAA/B,4BAAwB,IAC9BC,yBAAkES,EAAA,CAAvDC,KAAK,QAASC,QAAOC,EAAAe,kB,8BAAkB,IAAIb,EAAA,KAAAA,EAAA,I,6BAAJ,W,4DAGtD,IAMM,CANNf,gCAMM,MANN6B,EAMM,CALJ5B,yBAIEgB,EAAA,CAHAjF,IAAI,gBACHM,QAASuE,EAAAiB,4BACV7F,MAAM,S,sHAeL,GACbG,KAAM,iBACN2F,WAAY,CACVC,0BAEFtF,QACE,MAAMuF,EAAQC,iBAGRC,EAAmBnG,iBAAI,MACvBoG,EAAoBpG,iBAAI,MACxBqG,EAAgBrG,iBAAI,MACpBsG,EAAgBtG,iBAAI,MAEpBuG,EAAQC,sBAAS,IAAMP,EAAMQ,MAAMF,OAEnCG,EAAeF,sBAAS,IACrBP,EAAMU,QAAQC,oBAGjBC,EAAgBL,sBAAS,IACtBP,EAAMU,QAAQG,wBAGjBC,EAAYP,sBAAS,KAEzB,MAAMQ,EAAa,GAcnB,OAZAT,EAAMnF,MAAM6F,QAAQC,IACbF,EAAWE,EAAKC,YACnBH,EAAWE,EAAKC,UAAY,CAC1BC,MAAO,EACPC,SAAU,IAIdL,EAAWE,EAAKC,UAAUC,OAASF,EAAKE,MACxCJ,EAAWE,EAAKC,UAAUE,UAAYH,EAAKG,WAGtC7G,OAAO8G,QAAQN,GAAYjD,IAAI,EAAEoD,EAAUI,MAAU,CAC1DJ,WACAC,MAAOG,EAAKH,MACZC,SAAUE,EAAKF,SACfG,UAAWD,EAAKH,MAAQG,EAAKF,cAI3BI,EAAmBjB,sBAAS,IACzBkB,KAAKC,OAAOnH,OAAOoH,OAAOlB,EAAatF,OAAQ,IAGlDyG,EAAoBrB,sBAAS,IAC1BkB,KAAKC,OAAOnH,OAAOoH,OAAOf,EAAczF,OAAQ,IAGnD0G,EAAgBtB,sBAAS,IACE,IAA3BO,EAAU3F,MAAM2G,OAAqB,EAClCL,KAAKC,OAAOZ,EAAU3F,MAAM2C,IAAIiE,GAAQA,EAAKZ,OAAQ,IAIxDa,EAAiBA,KAAA,CACrBC,QAAS,CAAEC,SAAS,GACpBC,UAAW,CACTD,SAAS,EACTE,QAAS,CACPC,cAAe,CACbC,UAAW,CACT,iBACA,aACA,YACA,cACA,eACA,cACA,kBAKRC,WAAY,CACVC,MAAO,CAAC,CACNC,UAAW,CAAEC,SAAU,KACvBC,aAAc,CACZC,OAAQ,CAAEC,OAAQ,aAAcC,MAAO,SAAUC,cAAe,gBAOlE9D,EAAuBsB,sBAAS,KACpC,MAAMQ,EAAaxG,OAAOe,KAAKmF,EAAatF,OACtCmG,EAAO/G,OAAOoH,OAAOlB,EAAatF,OAExCN,QAAQC,IAAI,SAAU,CAAEiG,aAAYO,OAAMb,aAAcA,EAAatF,QAGrE,MAAM6H,EAAS,CACbrI,MAAO,CACLL,KAAM,SACN2I,OAAQ,KAEVC,MAAO,CACLC,KAAM,UAERC,SAAU,CACRD,KAAM,gBAERE,MAAO,CACLtC,WAAYA,EAAWe,OAAS,EAAIf,EAAa,CAAC,SAEpDuC,MAAO,CACLJ,MAAO,CAAEC,KAAM,QACfI,IAAK,GAEPC,OAAQ,CAAC,CACPrJ,KAAM,OACNmH,KAAMA,EAAKQ,OAAS,EAAIR,EAAO,CAAC,GAChCmC,cAAc,IAEhBxB,QAAS,CAAEC,SAAS,GACpBC,UAAW,CAAED,SAAS,GACtBwB,QAAS,CACPC,YAAa,yBAKjB,OADA9I,QAAQC,IAAI,SAAUkI,GACfA,IAIH3D,EAA2BkB,sBAAS,KACxC,MAAMqD,EAAmBrJ,OAAO8G,QAAQT,EAAczF,OAChD0I,EAAUD,EAAiB9B,OAAS,EACtC8B,EAAiB9F,IAAI,EAAE3D,EAAM2J,MAAO,CAAG3J,OAAM2J,OAC7C,CAAC,CAAE3J,KAAM,OAAQ2J,EAAG,IAIxB,OAFAjJ,QAAQC,IAAI,QAAS,CAAE8F,cAAeA,EAAczF,MAAO0I,YAEpD,IACF7B,IACHrH,MAAO,CACLL,KAAM,MACN2I,OAAQ,KAEVC,MAAO,CACLC,KAAM,SACNY,MAAO,CAAEC,SAAU,SAErBZ,SAAU,CACRD,KAAM,aACNY,MAAO,CAAEC,SAAU,SAErBR,OAAQ,CAAC,CACPrJ,KAAM,OACNmH,KAAMuC,EACNnF,KAAM,MACNuF,WAAY,CACV/B,SAAS,EACTgC,OAAQ,kDAGZC,YAAa,CACXC,IAAK,CACHC,kBAAkB,EAClBC,OAAQ,UACRC,cAAc,EACdN,WAAY,CACV/B,SAAS,EACTsC,SAAU,MAIhBd,QAAS,CACPC,YAAa,qDAMblE,EAA6Bc,sBAAS,KAC1C1F,QAAQC,IAAI,eAGZ,MAAMkI,EAAS,CACbrI,MAAO,CACLL,KAAM,QAER4I,MAAO,CACLC,KAAM,UAERE,MAAO,CACLtC,WAAY,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,OAE7CuC,MAAO,CACLJ,MAAO,CAAEC,KAAM,SAEjBK,OAAQ,CAAC,CACPrJ,KAAM,MACNmH,KAAM,CAAC,GAAI,GAAI,GAAI,GAAI,GAAI,KAC1B,CACDnH,KAAM,MACNmH,KAAM,CAAC,EAAG,GAAI,GAAI,GAAI,GAAI,OAK9B,OADAzG,QAAQC,IAAI,SAAUkI,GACfA,IAIHnD,EAA8BU,sBAAS,KAC3C1F,QAAQC,IAAI,gBAGZ,MAAMkI,EAAS,CACbE,MAAO,CACLC,KAAM,eAERE,MAAO,CACLtC,WAAY,CAAC,KAAM,KAAM,OAE3BuC,MAAO,CAAC,CACNJ,MAAO,CAAEC,KAAM,SACd,CACDD,MAAO,CAAEC,KAAM,QACfsB,UAAU,IAEZjB,OAAQ,CAAC,CACPrJ,KAAM,OACNG,KAAM,SACNgH,KAAM,CAAC,IAAK,GAAI,KAChBgC,MAAO,GACN,CACDnJ,KAAM,OACNG,KAAM,OACNgH,KAAM,CAAC,GAAI,GAAI,IACfgC,MAAO,KAKX,OADAzI,QAAQC,IAAI,UAAWkI,GAChBA,IAIHnE,EAAsBA,KAC1B6F,OAAUC,KAAK,uBAGXxF,EAAuBA,KAC3BuF,OAAUC,KAAK,uBAGXpF,EAAmBA,KACvBmF,OAAUC,KAAK,uBAGXhF,EAAmBA,KACvB+E,OAAUC,KAAK,uBAGjB,MAAO,CACLzE,mBACAC,oBACAC,gBACAC,gBACAI,eACAG,gBACAE,YACAU,mBACAI,oBACAC,gBACA5C,uBACAI,2BACAI,6BACAI,8BACAhB,sBACAM,uBACAI,mBACAI,sB,iCClXN,MAAMvD,EAA2B,IAAgB,EAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAE1E", "file": "js/chunk-52ea0bcc.cce8b994.js", "sourcesContent": ["<template>\n  <div ref=\"chartContainer\" class=\"highcharts-container\"></div>\n</template>\n\n<script>\nimport { ref, onMounted, onUnmounted, watch } from 'vue'\n\nexport default {\n  name: 'HighchartsWrapper',\n  props: {\n    options: {\n      type: Object,\n      required: true\n    }\n  },\n  setup(props) {\n    const chartContainer = ref(null)\n    let chart = null\n\n    const createChart = () => {\n      console.log('尝试创建图表...', {\n        hasHighcharts: !!window.Highcharts,\n        hasContainer: !!chartContainer.value,\n        optionsType: typeof props.options,\n        optionsKeys: props.options ? Object.keys(props.options) : []\n      })\n\n      if (window.Highcharts && chartContainer.value && props.options) {\n        try {\n          // 清空容器\n          chartContainer.value.innerHTML = ''\n\n          // 创建图表\n          chart = window.Highcharts.chart(chartContainer.value, props.options)\n          console.log('图表创建成功:', chart)\n        } catch (error) {\n          console.error('创建Highcharts图表失败:', error)\n          console.error('错误详情:', error.message)\n          console.error('图表配置:', props.options)\n\n          // 显示错误信息\n          if (chartContainer.value) {\n            chartContainer.value.innerHTML = `<div style=\"display: flex; align-items: center; justify-content: center; height: 100%; color: #f56c6c; flex-direction: column;\">\n              <div>图表创建失败</div>\n              <div style=\"font-size: 12px; margin-top: 5px;\">${error.message}</div>\n            </div>`\n          }\n        }\n      } else {\n        console.warn('Highcharts未加载或容器不存在或配置为空')\n        // 如果Highcharts还没加载，显示提示信息\n        if (chartContainer.value) {\n          chartContainer.value.innerHTML = '<div style=\"display: flex; align-items: center; justify-content: center; height: 100%; color: #666;\">正在加载图表...</div>'\n        }\n      }\n    }\n\n    const destroyChart = () => {\n      if (chart) {\n        chart.destroy()\n        chart = null\n      }\n    }\n\n    onMounted(() => {\n      console.log('HighchartsWrapper mounted')\n\n      // 检查Highcharts是否已加载的函数\n      const checkAndCreateChart = (retryCount = 0) => {\n        if (window.Highcharts) {\n          createChart()\n        } else if (retryCount < 10) {\n          console.log(`Highcharts未加载，重试 ${retryCount + 1}/10`)\n          setTimeout(() => {\n            checkAndCreateChart(retryCount + 1)\n          }, 200)\n        } else {\n          console.error('Highcharts加载失败，请检查CDN连接')\n          if (chartContainer.value) {\n            chartContainer.value.innerHTML = '<div style=\"display: flex; align-items: center; justify-content: center; height: 100%; color: #f56c6c;\">图表加载失败，请检查网络连接</div>'\n          }\n        }\n      }\n\n      checkAndCreateChart()\n    })\n\n    onUnmounted(() => {\n      destroyChart()\n    })\n\n    // 监听options变化\n    watch(() => props.options, () => {\n      destroyChart()\n      createChart()\n    }, { deep: true })\n\n    return {\n      chartContainer\n    }\n  }\n}\n</script>\n\n<style scoped>\n.highcharts-container {\n  width: 100%;\n  height: 100%;\n}\n</style>\n", "import { render } from \"./HighchartsWrapper.vue?vue&type=template&id=f326fe4a&scoped=true\"\nimport script from \"./HighchartsWrapper.vue?vue&type=script&lang=js\"\nexport * from \"./HighchartsWrapper.vue?vue&type=script&lang=js\"\n\nimport \"./HighchartsWrapper.vue?vue&type=style&index=0&id=f326fe4a&scoped=true&lang=css\"\n\nimport exportComponent from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\软件架构大作业\\\\homework\\\\node_modules\\\\vue-loader-v16\\\\dist\\\\exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-f326fe4a\"]])\n\nexport default __exports__", "export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader-v16/dist/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader-v16/dist/index.js??ref--1-1!./Statistics.vue?vue&type=style&index=0&id=ba9381b8&scoped=true&lang=css\"", "'use strict';\nvar $ = require('../internals/export');\nvar call = require('../internals/function-call');\nvar aCallable = require('../internals/a-callable');\nvar anObject = require('../internals/an-object');\nvar getIteratorDirect = require('../internals/get-iterator-direct');\nvar createIteratorProxy = require('../internals/iterator-create-proxy');\nvar callWithSafeIterationClosing = require('../internals/call-with-safe-iteration-closing');\nvar iteratorClose = require('../internals/iterator-close');\nvar iteratorHelperWithoutClosingOnEarlyError = require('../internals/iterator-helper-without-closing-on-early-error');\nvar IS_PURE = require('../internals/is-pure');\n\nvar mapWithoutClosingOnEarlyError = !IS_PURE && iteratorHelperWithoutClosingOnEarlyError('map', TypeError);\n\nvar IteratorProxy = createIteratorProxy(function () {\n  var iterator = this.iterator;\n  var result = anObject(call(this.next, iterator));\n  var done = this.done = !!result.done;\n  if (!done) return callWithSafeIterationClosing(iterator, this.mapper, [result.value, this.counter++], true);\n});\n\n// `Iterator.prototype.map` method\n// https://tc39.es/ecma262/#sec-iterator.prototype.map\n$({ target: 'Iterator', proto: true, real: true, forced: IS_PURE || mapWithoutClosingOnEarlyError }, {\n  map: function map(mapper) {\n    anObject(this);\n    try {\n      aCallable(mapper);\n    } catch (error) {\n      iteratorClose(this, 'throw', error);\n    }\n\n    if (mapWithoutClosingOnEarlyError) return call(mapWithoutClosingOnEarlyError, this, mapper);\n\n    return new IteratorProxy(getIteratorDirect(this), {\n      mapper: mapper\n    });\n  }\n});\n", "export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader-v16/dist/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader-v16/dist/index.js??ref--1-1!./HighchartsWrapper.vue?vue&type=style&index=0&id=f326fe4a&scoped=true&lang=css\"", "<template>\n  <div class=\"statistics-container\">\n    <h1>数据统计</h1>\n    <p class=\"page-description\">\n      使用Highcharts展示图书管理系统的各项数据统计，提供丰富的交互功能和专业的数据可视化效果。\n    </p>\n\n    <!-- 图书分类统计 - 使用Highcharts柱状图 -->\n    <el-row :gutter=\"20\">\n      <el-col :span=\"24\">\n        <el-card class=\"box-card\">\n          <template #header>\n            <div class=\"card-header\">\n              <span>图书分类统计 - Highcharts柱状图</span>\n              <el-button size=\"small\" @click=\"exportCategoryChart\">导出图表</el-button>\n            </div>\n          </template>\n          <div class=\"chart-container\">\n            <HighchartsWrapper\n              ref=\"categoryChartRef\"\n              :options=\"categoryChartOptions\"\n              class=\"chart\"\n            />\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <!-- 借阅统计 - 使用Highcharts饼图 -->\n    <el-row :gutter=\"20\" class=\"chart-row\">\n      <el-col :span=\"12\">\n        <el-card class=\"box-card\">\n          <template #header>\n            <div class=\"card-header\">\n              <span>借阅分布 - Highcharts饼图</span>\n              <el-button size=\"small\" @click=\"exportBorrowingChart\">导出图表</el-button>\n            </div>\n          </template>\n          <div class=\"chart-container\">\n            <HighchartsWrapper\n              ref=\"borrowingChartRef\"\n              :options=\"borrowingPieChartOptions\"\n              class=\"chart\"\n            />\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"12\">\n        <el-card class=\"box-card\">\n          <template #header>\n            <div class=\"card-header\">\n              <span>借阅趋势 - Highcharts折线图</span>\n              <el-button size=\"small\" @click=\"exportTrendChart\">导出图表</el-button>\n            </div>\n          </template>\n          <div class=\"chart-container\">\n            <HighchartsWrapper\n              ref=\"trendChartRef\"\n              :options=\"borrowingTrendChartOptions\"\n              class=\"chart\"\n            />\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <!-- 库存与借阅对比 - 使用Highcharts组合图表 -->\n    <el-row :gutter=\"20\" class=\"chart-row\">\n      <el-col :span=\"24\">\n        <el-card class=\"box-card\">\n          <template #header>\n            <div class=\"card-header\">\n              <span>库存与借阅对比 - Highcharts组合图表</span>\n              <el-button size=\"small\" @click=\"exportStockChart\">导出图表</el-button>\n            </div>\n          </template>\n          <div class=\"chart-container\">\n            <HighchartsWrapper\n              ref=\"stockChartRef\"\n              :options=\"stockComparisonChartOptions\"\n              class=\"chart\"\n            />\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n  </div>\n</template>\n\n<script>\nimport { computed, ref } from 'vue'\nimport { useStore } from 'vuex'\nimport { ElMessage } from 'element-plus'\nimport HighchartsWrapper from '@/components/HighchartsWrapper.vue'\n\nexport default {\n  name: 'StatisticsView',\n  components: {\n    HighchartsWrapper\n  },\n  setup() {\n    const store = useStore()\n\n    // 图表引用\n    const categoryChartRef = ref(null)\n    const borrowingChartRef = ref(null)\n    const trendChartRef = ref(null)\n    const stockChartRef = ref(null)\n\n    const books = computed(() => store.state.books)\n\n    const categoryData = computed(() => {\n      return store.getters.getBooksByCategory\n    })\n\n    const borrowingData = computed(() => {\n      return store.getters.getBorrowingStatistics\n    })\n\n    const stockData = computed(() => {\n      // 按分类统计库存和借阅\n      const categories = {}\n\n      books.value.forEach(book => {\n        if (!categories[book.category]) {\n          categories[book.category] = {\n            stock: 0,\n            borrowed: 0\n          }\n        }\n\n        categories[book.category].stock += book.stock\n        categories[book.category].borrowed += book.borrowed\n      })\n\n      return Object.entries(categories).map(([category, data]) => ({\n        category,\n        stock: data.stock,\n        borrowed: data.borrowed,\n        available: data.stock - data.borrowed\n      }))\n    })\n\n    const maxCategoryValue = computed(() => {\n      return Math.max(...Object.values(categoryData.value), 1)\n    })\n\n    const maxBorrowingValue = computed(() => {\n      return Math.max(...Object.values(borrowingData.value), 1)\n    })\n\n    const maxStockValue = computed(() => {\n      if (stockData.value.length === 0) return 1\n      return Math.max(...stockData.value.map(item => item.stock), 1)\n    })\n\n    // Highcharts基础配置\n    const getBaseOptions = () => ({\n      credits: { enabled: false },\n      exporting: {\n        enabled: true,\n        buttons: {\n          contextButton: {\n            menuItems: [\n              'viewFullscreen',\n              'printChart',\n              'separator',\n              'downloadPNG',\n              'downloadJPEG',\n              'downloadPDF',\n              'downloadSVG'\n            ]\n          }\n        }\n      },\n      responsive: {\n        rules: [{\n          condition: { maxWidth: 500 },\n          chartOptions: {\n            legend: { layout: 'horizontal', align: 'center', verticalAlign: 'bottom' }\n          }\n        }]\n      }\n    })\n\n    // 图书分类统计柱状图配置\n    const categoryChartOptions = computed(() => {\n      const categories = Object.keys(categoryData.value)\n      const data = Object.values(categoryData.value)\n\n      console.log('柱状图数据:', { categories, data, categoryData: categoryData.value })\n\n      // 简化的配置，确保基本功能正常\n      const config = {\n        chart: {\n          type: 'column',\n          height: 350\n        },\n        title: {\n          text: '图书分类统计'\n        },\n        subtitle: {\n          text: '展示各类别图书的数量分布'\n        },\n        xAxis: {\n          categories: categories.length > 0 ? categories : ['暂无数据']\n        },\n        yAxis: {\n          title: { text: '图书数量' },\n          min: 0\n        },\n        series: [{\n          name: '图书数量',\n          data: data.length > 0 ? data : [0],\n          colorByPoint: true\n        }],\n        credits: { enabled: false },\n        exporting: { enabled: true },\n        tooltip: {\n          pointFormat: '<b>{point.y}</b> 本图书'\n        }\n      }\n\n      console.log('柱状图配置:', config)\n      return config\n    })\n\n    // 借阅分布饼图配置\n    const borrowingPieChartOptions = computed(() => {\n      const borrowingEntries = Object.entries(borrowingData.value)\n      const pieData = borrowingEntries.length > 0\n        ? borrowingEntries.map(([name, y]) => ({ name, y }))\n        : [{ name: '暂无数据', y: 1 }]\n\n      console.log('饼图数据:', { borrowingData: borrowingData.value, pieData })\n\n      return {\n        ...getBaseOptions(),\n        chart: {\n          type: 'pie',\n          height: 350\n        },\n        title: {\n          text: '借阅分布统计',\n          style: { fontSize: '16px' }\n        },\n        subtitle: {\n          text: '各类别图书的借阅比例',\n          style: { fontSize: '12px' }\n        },\n        series: [{\n          name: '借阅数量',\n          data: pieData,\n          size: '80%',\n          dataLabels: {\n            enabled: true,\n            format: '<b>{point.name}</b>: {point.percentage:.1f}%'\n          }\n        }],\n        plotOptions: {\n          pie: {\n            allowPointSelect: true,\n            cursor: 'pointer',\n            showInLegend: true,\n            dataLabels: {\n              enabled: true,\n              distance: 20\n            }\n          }\n        },\n        tooltip: {\n          pointFormat: '<b>{point.y}</b> 次借阅 ({point.percentage:.1f}%)'\n        }\n      }\n    })\n\n    // 借阅趋势折线图配置\n    const borrowingTrendChartOptions = computed(() => {\n      console.log('折线图配置生成中...')\n\n      // 最简单的折线图配置\n      const config = {\n        chart: {\n          type: 'line'\n        },\n        title: {\n          text: '借阅趋势分析'\n        },\n        xAxis: {\n          categories: ['1月', '2月', '3月', '4月', '5月', '6月']\n        },\n        yAxis: {\n          title: { text: '借阅次数' }\n        },\n        series: [{\n          name: '科幻类',\n          data: [10, 15, 12, 18, 22, 25]\n        }, {\n          name: '文学类',\n          data: [8, 12, 16, 14, 20, 18]\n        }]\n      }\n\n      console.log('折线图配置:', config)\n      return config\n    })\n\n    // 库存对比组合图表配置\n    const stockComparisonChartOptions = computed(() => {\n      console.log('组合图表配置生成中...')\n\n      // 简化的组合图表配置\n      const config = {\n        title: {\n          text: '图书库存与借阅对比分析'\n        },\n        xAxis: {\n          categories: ['科幻', '历史', '文学']\n        },\n        yAxis: [{\n          title: { text: '库存数量' }\n        }, {\n          title: { text: '借阅次数' },\n          opposite: true\n        }],\n        series: [{\n          name: '库存数量',\n          type: 'column',\n          data: [120, 90, 150],\n          yAxis: 0\n        }, {\n          name: '借阅次数',\n          type: 'line',\n          data: [40, 30, 50],\n          yAxis: 1\n        }]\n      }\n\n      console.log('组合图表配置:', config)\n      return config\n    })\n\n    // 导出方法\n    const exportCategoryChart = () => {\n      ElMessage.info('图表导出功能已集成在图表右上角菜单中')\n    }\n\n    const exportBorrowingChart = () => {\n      ElMessage.info('图表导出功能已集成在图表右上角菜单中')\n    }\n\n    const exportTrendChart = () => {\n      ElMessage.info('图表导出功能已集成在图表右上角菜单中')\n    }\n\n    const exportStockChart = () => {\n      ElMessage.info('图表导出功能已集成在图表右上角菜单中')\n    }\n\n    return {\n      categoryChartRef,\n      borrowingChartRef,\n      trendChartRef,\n      stockChartRef,\n      categoryData,\n      borrowingData,\n      stockData,\n      maxCategoryValue,\n      maxBorrowingValue,\n      maxStockValue,\n      categoryChartOptions,\n      borrowingPieChartOptions,\n      borrowingTrendChartOptions,\n      stockComparisonChartOptions,\n      exportCategoryChart,\n      exportBorrowingChart,\n      exportTrendChart,\n      exportStockChart\n    }\n  }\n}\n</script>\n\n<style scoped>\n.statistics-container {\n  padding: 20px;\n}\n\n.page-description {\n  font-size: 16px;\n  color: #666;\n  margin-bottom: 20px;\n  line-height: 1.6;\n  background: #f8f9fa;\n  padding: 15px;\n  border-radius: 8px;\n  border-left: 4px solid #409EFF;\n}\n\n.chart-row {\n  margin-top: 20px;\n}\n\n.chart-container {\n  height: 400px;\n  padding: 10px;\n  position: relative;\n}\n\n.chart {\n  height: 100%;\n  width: 100%;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-weight: bold;\n  color: #303133;\n}\n\n.card-header span {\n  font-size: 16px;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .statistics-container {\n    padding: 10px;\n  }\n\n  .chart-container {\n    height: 300px;\n  }\n\n  .page-description {\n    font-size: 14px;\n    padding: 10px;\n  }\n}\n</style>\n", "import { render } from \"./Statistics.vue?vue&type=template&id=ba9381b8&scoped=true\"\nimport script from \"./Statistics.vue?vue&type=script&lang=js\"\nexport * from \"./Statistics.vue?vue&type=script&lang=js\"\n\nimport \"./Statistics.vue?vue&type=style&index=0&id=ba9381b8&scoped=true&lang=css\"\n\nimport exportComponent from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\软件架构大作业\\\\homework\\\\node_modules\\\\vue-loader-v16\\\\dist\\\\exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-ba9381b8\"]])\n\nexport default __exports__"], "sourceRoot": ""}