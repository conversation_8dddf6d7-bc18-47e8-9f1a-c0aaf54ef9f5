{"version": 3, "sources": ["webpack:///./src/views/Users.vue?b7ba", "webpack:///./src/views/Users.vue", "webpack:///./src/views/Users.vue?3fde"], "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_createVNode", "_component_el_button", "type", "onClick", "_cache", "$event", "$setup", "dialogVisible", "_component_el_input", "searchQuery", "placeholder", "clearable", "prefix", "_withCtx", "_component_el_icon", "_component_search", "_component_el_table", "data", "filteredUsers", "style", "border", "_component_el_table_column", "prop", "label", "width", "default", "scope", "row", "borrowedBooks", "length", "_Fragment", "_renderList", "bookId", "_createBlock", "_component_el_tag", "key", "getBookTitle", "_hoisted_3", "size", "editUser", "confirmDelete", "id", "disabled", "_component_el_dialog", "title", "isEditing", "footer", "_hoisted_4", "saveUser", "_component_el_form", "model", "userForm", "label-width", "_component_el_form_item", "name", "contact", "components", "Search", "setup", "store", "useStore", "ref", "users", "computed", "state", "books", "value", "query", "toLowerCase", "filter", "user", "includes", "book", "find", "b", "resetForm", "dispatch", "ElMessage", "success", "userId", "u", "warning", "ElMessageBox", "confirm", "confirmButtonText", "cancelButtonText", "then", "catch", "info", "__exports__", "render"], "mappings": "uIAAA,W,kECCOA,MAAM,mB,GAGJA,MAAM,c,aA+DDA,MAAM,iB,6fAlElBC,gCAwEM,MAxENC,EAwEM,C,cAvEJC,gCAAa,UAAT,QAAI,IAERA,gCAYM,MAZNC,EAYM,CAXJC,yBAAwEC,EAAA,CAA7DC,KAAK,UAAWC,QAAKC,EAAA,KAAAA,EAAA,GAAAC,GAAEC,EAAAC,eAAgB,I,8BAAM,IAAIH,EAAA,KAAAA,EAAA,I,6BAAJ,W,aACxDJ,yBASWQ,EAAA,C,WARAF,EAAAG,Y,qCAAAH,EAAAG,YAAWJ,GACpBK,YAAY,OACZf,MAAM,eACNgB,UAAA,I,CAEWC,OAAMC,qBACf,IAA6B,CAA7Bb,yBAA6Bc,EAAA,M,6BAApB,IAAU,CAAVd,yBAAUe,K,iCAKzBf,yBAgCWgB,EAAA,CAhCAC,KAAMX,EAAAY,cAAeC,MAAA,eAAoBC,OAAA,I,8BAClD,IAAmD,CAAnDpB,yBAAmDqB,EAAA,CAAlCC,KAAK,KAAKC,MAAM,KAAKC,MAAM,OAC5CxB,yBAA0CqB,EAAA,CAAzBC,KAAK,OAAOC,MAAM,OACnCvB,yBAA+CqB,EAAA,CAA9BC,KAAK,UAAUC,MAAM,SACtCvB,yBAIkBqB,EAAA,CAJDE,MAAM,QAAM,CAChBE,QAAOZ,qBAAEa,GAAK,C,0DACpBA,EAAMC,IAAIC,cAAcC,QAAM,K,MAGrC7B,yBAWkBqB,EAAA,CAXDE,MAAM,QAAM,CAChBE,QAAOZ,qBAAEa,GAAK,E,2BACvB9B,gCAMSkC,cAAA,KAAAC,wBALUL,EAAMC,IAAIC,cAApBI,I,yBADTC,yBAMSC,EAAA,CAJNC,IAAKH,EACNrC,MAAM,Y,8BAEN,IAA0B,C,0DAAvBW,EAAA8B,aAAaJ,IAAM,K,mBAEuB,IAAnCN,EAAMC,IAAIC,cAAcC,Q,yBAApCjC,gCAA4D,OAAAyC,EAAV,Q,+CAGtDrC,yBAUkBqB,EAAA,CAVDE,MAAM,KAAKC,MAAM,O,CACrBC,QAAOZ,qBAAEa,GAAK,CACvB1B,yBAAmEC,EAAA,CAAxDqC,KAAK,QAASnC,QAAKE,GAAEC,EAAAiC,SAASb,EAAMC,M,8BAAM,IAAEvB,EAAA,KAAAA,EAAA,I,6BAAF,S,8BACrDJ,yBAKeC,EAAA,CAJbqC,KAAK,QACLpC,KAAK,SACJC,QAAKE,GAAEC,EAAAkC,cAAcd,EAAMC,IAAIc,IAC/BC,SAAUhB,EAAMC,IAAIC,cAAcC,OAAS,G,8BAC7C,IAAEzB,EAAA,KAAAA,EAAA,I,6BAAF,S,oEAMPJ,yBAmBY2C,EAAA,C,WAlBDrC,EAAAC,c,qCAAAD,EAAAC,cAAaF,GACrBuC,MAAOtC,EAAAuC,UAAY,OAAS,OAC7BrB,MAAM,O,CAUKsB,OAAMjC,qBACf,IAGO,CAHPf,gCAGO,OAHPiD,EAGO,CAFL/C,yBAAwDC,EAAA,CAA5CE,QAAKC,EAAA,KAAAA,EAAA,GAAAC,GAAEC,EAAAC,eAAgB,I,8BAAO,IAAEH,EAAA,KAAAA,EAAA,I,6BAAF,S,aAC1CJ,yBAA0DC,EAAA,CAA/CC,KAAK,UAAWC,QAAOG,EAAA0C,U,8BAAU,IAAE5C,EAAA,MAAAA,EAAA,K,6BAAF,S,6DAXhD,IAOU,CAPVJ,yBAOUiD,EAAA,CAPAC,MAAO5C,EAAA6C,SAAUC,cAAY,S,8BACrC,IAEe,CAFfpD,yBAEeqD,EAAA,CAFD9B,MAAM,MAAI,C,6BACtB,IAAoC,CAApCvB,yBAAoCQ,EAAA,C,WAAjBF,EAAA6C,SAASG,K,qCAAThD,EAAA6C,SAASG,KAAIjD,I,+BAElCL,yBAEeqD,EAAA,CAFD9B,MAAM,QAAM,C,6BACxB,IAAuC,CAAvCvB,yBAAuCQ,EAAA,C,WAApBF,EAAA6C,SAASI,Q,qCAATjD,EAAA6C,SAASI,QAAOlD,I,wKAmB9B,GACbiD,KAAM,YACNE,WAAY,CACVC,oBAEFC,QACE,MAAMC,EAAQC,iBACRrD,EAAgBsD,kBAAI,GACpBhB,EAAYgB,kBAAI,GAChBpD,EAAcoD,iBAAI,IAElBV,EAAWU,iBAAI,CACnBpB,GAAI,KACJa,KAAM,GACNC,QAAS,GACT3B,cAAe,KAGXkC,EAAQC,sBAAS,IAAMJ,EAAMK,MAAMF,OACnCG,EAAQF,sBAAS,IAAMJ,EAAMK,MAAMC,OAEnC/C,EAAgB6C,sBAAS,KAC7B,IAAKtD,EAAYyD,MAAO,OAAOJ,EAAMI,MAErC,MAAMC,EAAQ1D,EAAYyD,MAAME,cAChC,OAAON,EAAMI,MAAMG,OAAOC,GACxBA,EAAKhB,KAAKc,cAAcG,SAASJ,IACjCG,EAAKf,QAAQa,cAAcG,SAASJ,MAIlC/B,EAAgBJ,IACpB,MAAMwC,EAAOP,EAAMC,MAAMO,KAAKC,GAAKA,EAAEjC,KAAOT,GAC5C,OAAOwC,EAAOA,EAAK5B,MAAQ,QAGvB+B,EAAYA,KAChBxB,EAASe,MAAQ,CACfzB,GAAI,KACJa,KAAM,GACNC,QAAS,GACT3B,cAAe,KAIbW,EAAY+B,IAChBzB,EAAUqB,OAAQ,EAClBf,EAASe,MAAQ,IAAKI,GACtB/D,EAAc2D,OAAQ,GAGlBlB,EAAWA,KACXH,EAAUqB,OACZP,EAAMiB,SAAS,aAAczB,EAASe,OACtCW,OAAUC,QAAQ,YAElBnB,EAAMiB,SAAS,UAAWzB,EAASe,OACnCW,OAAUC,QAAQ,WAGpBvE,EAAc2D,OAAQ,EACtBS,IACA9B,EAAUqB,OAAQ,GAGd1B,EAAiBuC,IACrB,MAAMT,EAAOR,EAAMI,MAAMO,KAAKO,GAAKA,EAAEvC,KAAOsC,GAExCT,EAAK1C,cAAcC,OAAS,EAC9BgD,OAAUI,QAAQ,mBAIpBC,OAAaC,QACX,qBACA,KACA,CACEC,kBAAmB,KACnBC,iBAAkB,KAClBnF,KAAM,YAGPoF,KAAK,KACJ3B,EAAMiB,SAAS,aAAcG,GAC7BF,OAAUC,QAAQ,YAEnBS,MAAM,KACLV,OAAUW,KAAK,YAIrB,MAAO,CACLjF,gBACAsC,YACAM,WACAW,QACA5C,gBACAT,cACA2B,eACAG,WACAS,WACAR,mB,iCChLN,MAAMiD,EAA2B,IAAgB,EAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAE1E", "file": "js/chunk-1dea58d1.97295288.js", "sourcesContent": ["export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader-v16/dist/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader-v16/dist/index.js??ref--1-1!./Users.vue?vue&type=style&index=0&id=debab2f2&scoped=true&lang=css\"", "<template>\n  <div class=\"users-container\">\n    <h1>用户管理</h1>\n    \n    <div class=\"action-bar\">\n      <el-button type=\"primary\" @click=\"dialogVisible = true\">添加用户</el-button>\n      <el-input\n        v-model=\"searchQuery\"\n        placeholder=\"搜索用户\"\n        class=\"search-input\"\n        clearable\n      >\n        <template #prefix>\n          <el-icon><search /></el-icon>\n        </template>\n      </el-input>\n    </div>\n    \n    <el-table :data=\"filteredUsers\" style=\"width: 100%\" border>\n      <el-table-column prop=\"id\" label=\"ID\" width=\"80\" />\n      <el-table-column prop=\"name\" label=\"姓名\" />\n      <el-table-column prop=\"contact\" label=\"联系方式\" />\n      <el-table-column label=\"借阅数量\">\n        <template #default=\"scope\">\n          {{ scope.row.borrowedBooks.length }}\n        </template>\n      </el-table-column>\n      <el-table-column label=\"借阅图书\">\n        <template #default=\"scope\">\n          <el-tag\n            v-for=\"bookId in scope.row.borrowedBooks\"\n            :key=\"bookId\"\n            class=\"book-tag\"\n          >\n            {{ getBookTitle(bookId) }}\n          </el-tag>\n          <span v-if=\"scope.row.borrowedBooks.length === 0\">无借阅</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" width=\"200\">\n        <template #default=\"scope\">\n          <el-button size=\"small\" @click=\"editUser(scope.row)\">编辑</el-button>\n          <el-button\n            size=\"small\"\n            type=\"danger\"\n            @click=\"confirmDelete(scope.row.id)\"\n            :disabled=\"scope.row.borrowedBooks.length > 0\"\n          >删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    \n    <!-- 添加/编辑用户对话框 -->\n    <el-dialog\n      v-model=\"dialogVisible\"\n      :title=\"isEditing ? '编辑用户' : '添加用户'\"\n      width=\"50%\"\n    >\n      <el-form :model=\"userForm\" label-width=\"120px\">\n        <el-form-item label=\"姓名\">\n          <el-input v-model=\"userForm.name\" />\n        </el-form-item>\n        <el-form-item label=\"联系方式\">\n          <el-input v-model=\"userForm.contact\" />\n        </el-form-item>\n      </el-form>\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"dialogVisible = false\">取消</el-button>\n          <el-button type=\"primary\" @click=\"saveUser\">确定</el-button>\n        </span>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { ref, computed } from 'vue'\nimport { useStore } from 'vuex'\nimport { ElMessageBox, ElMessage } from 'element-plus'\nimport { Search } from '@element-plus/icons-vue'\n\nexport default {\n  name: 'UsersView',\n  components: {\n    Search\n  },\n  setup() {\n    const store = useStore()\n    const dialogVisible = ref(false)\n    const isEditing = ref(false)\n    const searchQuery = ref('')\n    \n    const userForm = ref({\n      id: null,\n      name: '',\n      contact: '',\n      borrowedBooks: []\n    })\n    \n    const users = computed(() => store.state.users)\n    const books = computed(() => store.state.books)\n    \n    const filteredUsers = computed(() => {\n      if (!searchQuery.value) return users.value\n      \n      const query = searchQuery.value.toLowerCase()\n      return users.value.filter(user => \n        user.name.toLowerCase().includes(query) ||\n        user.contact.toLowerCase().includes(query)\n      )\n    })\n    \n    const getBookTitle = (bookId) => {\n      const book = books.value.find(b => b.id === bookId)\n      return book ? book.title : '未知图书'\n    }\n    \n    const resetForm = () => {\n      userForm.value = {\n        id: null,\n        name: '',\n        contact: '',\n        borrowedBooks: []\n      }\n    }\n    \n    const editUser = (user) => {\n      isEditing.value = true\n      userForm.value = { ...user }\n      dialogVisible.value = true\n    }\n    \n    const saveUser = () => {\n      if (isEditing.value) {\n        store.dispatch('updateUser', userForm.value)\n        ElMessage.success('用户更新成功')\n      } else {\n        store.dispatch('addUser', userForm.value)\n        ElMessage.success('用户添加成功')\n      }\n      \n      dialogVisible.value = false\n      resetForm()\n      isEditing.value = false\n    }\n    \n    const confirmDelete = (userId) => {\n      const user = users.value.find(u => u.id === userId)\n      \n      if (user.borrowedBooks.length > 0) {\n        ElMessage.warning('该用户有未归还的图书，无法删除')\n        return\n      }\n      \n      ElMessageBox.confirm(\n        '确定要删除这个用户吗？此操作不可逆。',\n        '警告',\n        {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning',\n        }\n      )\n        .then(() => {\n          store.dispatch('deleteUser', userId)\n          ElMessage.success('用户删除成功')\n        })\n        .catch(() => {\n          ElMessage.info('已取消删除')\n        })\n    }\n    \n    return {\n      dialogVisible,\n      isEditing,\n      userForm,\n      users,\n      filteredUsers,\n      searchQuery,\n      getBookTitle,\n      editUser,\n      saveUser,\n      confirmDelete\n    }\n  }\n}\n</script>\n\n<style scoped>\n.users-container {\n  padding: 20px;\n}\n\n.action-bar {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 20px;\n}\n\n.search-input {\n  width: 300px;\n}\n\n.book-tag {\n  margin-right: 5px;\n  margin-bottom: 5px;\n}\n</style>\n", "import { render } from \"./Users.vue?vue&type=template&id=debab2f2&scoped=true\"\nimport script from \"./Users.vue?vue&type=script&lang=js\"\nexport * from \"./Users.vue?vue&type=script&lang=js\"\n\nimport \"./Users.vue?vue&type=style&index=0&id=debab2f2&scoped=true&lang=css\"\n\nimport exportComponent from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\软件架构大作业\\\\homework\\\\node_modules\\\\vue-loader-v16\\\\dist\\\\exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-debab2f2\"]])\n\nexport default __exports__"], "sourceRoot": ""}