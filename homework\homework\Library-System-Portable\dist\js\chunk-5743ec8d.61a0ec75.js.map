{"version": 3, "sources": ["webpack:///./src/views/Books.vue", "webpack:///./src/views/Books.vue?9e66", "webpack:///./src/views/Books.vue?ba79"], "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_createVNode", "_component_el_button", "type", "onClick", "_cache", "$event", "$setup", "dialogVisible", "_component_el_input", "searchQuery", "placeholder", "clearable", "prefix", "_withCtx", "_component_el_icon", "_component_search", "_component_el_table", "data", "filteredBooks", "style", "border", "_component_el_table_column", "prop", "label", "width", "default", "scope", "_component_el_tag", "row", "stock", "borrowed", "_toDisplayString", "size", "editBook", "confirmDelete", "id", "_component_el_dialog", "title", "isEditing", "footer", "_hoisted_3", "saveBook", "_component_el_form", "model", "bookForm", "label-width", "_component_el_form_item", "author", "category", "publisher", "isbn", "_component_el_input_number", "min", "name", "components", "Search", "setup", "store", "useStore", "ref", "books", "computed", "state", "value", "query", "toLowerCase", "filter", "book", "includes", "resetForm", "dispatch", "ElMessage", "success", "bookId", "ElMessageBox", "confirm", "confirmButtonText", "cancelButtonText", "then", "catch", "info", "__exports__", "render"], "mappings": "2KACOA,MAAM,mB,GAGJA,MAAM,c,GAmEDA,MAAM,iB,gjBAtElBC,gCA4EM,MA5ENC,EA4EM,C,cA3EJC,gCAAa,UAAT,QAAI,IAERA,gCAYM,MAZNC,EAYM,CAXJC,yBAAwEC,EAAA,CAA7DC,KAAK,UAAWC,QAAKC,EAAA,KAAAA,EAAA,GAAAC,GAAEC,EAAAC,eAAgB,I,8BAAM,IAAIH,EAAA,MAAAA,EAAA,K,6BAAJ,W,cACxDJ,yBASWQ,EAAA,C,WARAF,EAAAG,Y,qCAAAH,EAAAG,YAAWJ,GACpBK,YAAY,OACZf,MAAM,eACNgB,UAAA,I,CAEWC,OAAMC,qBACf,IAA6B,CAA7Bb,yBAA6Bc,EAAA,M,6BAApB,IAAU,CAAVd,yBAAUe,K,iCAKzBf,yBAwBWgB,EAAA,CAxBAC,KAAMX,EAAAY,cAAeC,MAAA,eAAoBC,OAAA,I,8BAClD,IAAmD,CAAnDpB,yBAAmDqB,EAAA,CAAlCC,KAAK,KAAKC,MAAM,KAAKC,MAAM,OAC5CxB,yBAA2CqB,EAAA,CAA1BC,KAAK,QAAQC,MAAM,OACpCvB,yBAA4CqB,EAAA,CAA3BC,KAAK,SAASC,MAAM,OACrCvB,yBAA8CqB,EAAA,CAA7BC,KAAK,WAAWC,MAAM,OACvCvB,yBAAgDqB,EAAA,CAA/BC,KAAK,YAAYC,MAAM,QACxCvB,yBAA4CqB,EAAA,CAA3BC,KAAK,OAAOC,MAAM,SACnCvB,yBAMkBqB,EAAA,CANDE,MAAM,QAAM,CAChBE,QAAOZ,qBAAEa,GAAK,CACvB1B,yBAES2B,EAAA,CAFAzB,KAAMwB,EAAME,IAAIC,MAAQH,EAAME,IAAIE,SAAW,UAAY,U,8BAChE,IAAwB,C,0DAArBJ,EAAME,IAAIE,UAAW,IAACC,6BAAGL,EAAME,IAAIC,OAAK,K,4BAIjD7B,yBASkBqB,EAAA,CATDE,MAAM,KAAKC,MAAM,O,CACrBC,QAAOZ,qBAAEa,GAAK,CACvB1B,yBAAmEC,EAAA,CAAxD+B,KAAK,QAAS7B,QAAKE,GAAEC,EAAA2B,SAASP,EAAME,M,8BAAM,IAAExB,EAAA,MAAAA,EAAA,K,6BAAF,S,+BACrDJ,yBAIeC,EAAA,CAHb+B,KAAK,QACL9B,KAAK,SACJC,QAAKE,GAAEC,EAAA4B,cAAcR,EAAME,IAAIO,K,8BACjC,IAAE/B,EAAA,MAAAA,EAAA,K,6BAAF,S,0DAMPJ,yBA+BYoC,EAAA,C,WA9BD9B,EAAAC,c,qCAAAD,EAAAC,cAAaF,GACrBgC,MAAO/B,EAAAgC,UAAY,OAAS,OAC7Bd,MAAM,O,CAsBKe,OAAM1B,qBACf,IAGO,CAHPf,gCAGO,OAHP0C,EAGO,CAFLxC,yBAAwDC,EAAA,CAA5CE,QAAKC,EAAA,KAAAA,EAAA,GAAAC,GAAEC,EAAAC,eAAgB,I,8BAAO,IAAEH,EAAA,MAAAA,EAAA,K,6BAAF,S,cAC1CJ,yBAA0DC,EAAA,CAA/CC,KAAK,UAAWC,QAAOG,EAAAmC,U,8BAAU,IAAErC,EAAA,MAAAA,EAAA,K,6BAAF,S,6DAvBhD,IAmBU,CAnBVJ,yBAmBU0C,EAAA,CAnBAC,MAAOrC,EAAAsC,SAAUC,cAAY,S,8BACrC,IAEe,CAFf7C,yBAEe8C,EAAA,CAFDvB,MAAM,MAAI,C,6BACtB,IAAqC,CAArCvB,yBAAqCQ,EAAA,C,WAAlBF,EAAAsC,SAASP,M,qCAAT/B,EAAAsC,SAASP,MAAKhC,I,+BAEnCL,yBAEe8C,EAAA,CAFDvB,MAAM,MAAI,C,6BACtB,IAAsC,CAAtCvB,yBAAsCQ,EAAA,C,WAAnBF,EAAAsC,SAASG,O,qCAATzC,EAAAsC,SAASG,OAAM1C,I,+BAEpCL,yBAEe8C,EAAA,CAFDvB,MAAM,MAAI,C,6BACtB,IAAwC,CAAxCvB,yBAAwCQ,EAAA,C,WAArBF,EAAAsC,SAASI,S,qCAAT1C,EAAAsC,SAASI,SAAQ3C,I,+BAEtCL,yBAEe8C,EAAA,CAFDvB,MAAM,OAAK,C,6BACvB,IAAyC,CAAzCvB,yBAAyCQ,EAAA,C,WAAtBF,EAAAsC,SAASK,U,qCAAT3C,EAAAsC,SAASK,UAAS5C,I,+BAEvCL,yBAEe8C,EAAA,CAFDvB,MAAM,QAAM,C,6BACxB,IAAoC,CAApCvB,yBAAoCQ,EAAA,C,WAAjBF,EAAAsC,SAASM,K,qCAAT5C,EAAAsC,SAASM,KAAI7C,I,+BAElCL,yBAEe8C,EAAA,CAFDvB,MAAM,QAAM,C,6BACxB,IAAqD,CAArDvB,yBAAqDmD,EAAA,C,WAA3B7C,EAAAsC,SAASf,M,qCAATvB,EAAAsC,SAASf,MAAKxB,GAAG+C,IAAK,G,8JAmB3C,GACbC,KAAM,YACNC,WAAY,CACVC,oBAEFC,QACE,MAAMC,EAAQC,iBACRnD,EAAgBoD,kBAAI,GACpBrB,EAAYqB,kBAAI,GAChBlD,EAAckD,iBAAI,IAElBf,EAAWe,iBAAI,CACnBxB,GAAI,KACJE,MAAO,GACPU,OAAQ,GACRC,SAAU,GACVC,UAAW,GACXC,KAAM,GACNrB,MAAO,EACPC,SAAU,IAGN8B,EAAQC,sBAAS,IAAMJ,EAAMK,MAAMF,OAEnC1C,EAAgB2C,sBAAS,KAC7B,IAAKpD,EAAYsD,MAAO,OAAOH,EAAMG,MAErC,MAAMC,EAAQvD,EAAYsD,MAAME,cAChC,OAAOL,EAAMG,MAAMG,OAAOC,GACxBA,EAAK9B,MAAM4B,cAAcG,SAASJ,IAClCG,EAAKpB,OAAOkB,cAAcG,SAASJ,IACnCG,EAAKnB,SAASiB,cAAcG,SAASJ,IACrCG,EAAKjB,KAAKe,cAAcG,SAASJ,MAI/BK,EAAYA,KAChBzB,EAASmB,MAAQ,CACf5B,GAAI,KACJE,MAAO,GACPU,OAAQ,GACRC,SAAU,GACVC,UAAW,GACXC,KAAM,GACNrB,MAAO,EACPC,SAAU,IAIRG,EAAYkC,IAChB7B,EAAUyB,OAAQ,EAClBnB,EAASmB,MAAQ,IAAKI,GACtB5D,EAAcwD,OAAQ,GAGlBtB,EAAWA,KACXH,EAAUyB,OACZN,EAAMa,SAAS,aAAc1B,EAASmB,OACtCQ,OAAUC,QAAQ,YAElBf,EAAMa,SAAS,UAAW1B,EAASmB,OACnCQ,OAAUC,QAAQ,WAGpBjE,EAAcwD,OAAQ,EACtBM,IACA/B,EAAUyB,OAAQ,GAGd7B,EAAiBuC,IACrBC,OAAaC,QACX,qBACA,KACA,CACEC,kBAAmB,KACnBC,iBAAkB,KAClB3E,KAAM,YAGP4E,KAAK,KACJrB,EAAMa,SAAS,aAAcG,GAC7BF,OAAUC,QAAQ,YAEnBO,MAAM,KACLR,OAAUS,KAAK,YAIrB,MAAO,CACLzE,gBACA+B,YACAM,WACAgB,QACA1C,gBACAT,cACAwB,WACAQ,WACAP,mB,iCChLN,MAAM+C,EAA2B,IAAgB,EAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAE1E,gB,kCCTf", "file": "js/chunk-5743ec8d.61a0ec75.js", "sourcesContent": ["<template>\n  <div class=\"books-container\">\n    <h1>图书管理</h1>\n    \n    <div class=\"action-bar\">\n      <el-button type=\"primary\" @click=\"dialogVisible = true\">添加图书</el-button>\n      <el-input\n        v-model=\"searchQuery\"\n        placeholder=\"搜索图书\"\n        class=\"search-input\"\n        clearable\n      >\n        <template #prefix>\n          <el-icon><search /></el-icon>\n        </template>\n      </el-input>\n    </div>\n    \n    <el-table :data=\"filteredBooks\" style=\"width: 100%\" border>\n      <el-table-column prop=\"id\" label=\"ID\" width=\"80\" />\n      <el-table-column prop=\"title\" label=\"书名\" />\n      <el-table-column prop=\"author\" label=\"作者\" />\n      <el-table-column prop=\"category\" label=\"分类\" />\n      <el-table-column prop=\"publisher\" label=\"出版社\" />\n      <el-table-column prop=\"isbn\" label=\"ISBN\" />\n      <el-table-column label=\"库存状态\">\n        <template #default=\"scope\">\n          <el-tag :type=\"scope.row.stock > scope.row.borrowed ? 'success' : 'danger'\">\n            {{ scope.row.borrowed }}/{{ scope.row.stock }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" width=\"200\">\n        <template #default=\"scope\">\n          <el-button size=\"small\" @click=\"editBook(scope.row)\">编辑</el-button>\n          <el-button\n            size=\"small\"\n            type=\"danger\"\n            @click=\"confirmDelete(scope.row.id)\"\n          >删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    \n    <!-- 添加/编辑图书对话框 -->\n    <el-dialog\n      v-model=\"dialogVisible\"\n      :title=\"isEditing ? '编辑图书' : '添加图书'\"\n      width=\"50%\"\n    >\n      <el-form :model=\"bookForm\" label-width=\"120px\">\n        <el-form-item label=\"书名\">\n          <el-input v-model=\"bookForm.title\" />\n        </el-form-item>\n        <el-form-item label=\"作者\">\n          <el-input v-model=\"bookForm.author\" />\n        </el-form-item>\n        <el-form-item label=\"分类\">\n          <el-input v-model=\"bookForm.category\" />\n        </el-form-item>\n        <el-form-item label=\"出版社\">\n          <el-input v-model=\"bookForm.publisher\" />\n        </el-form-item>\n        <el-form-item label=\"ISBN\">\n          <el-input v-model=\"bookForm.isbn\" />\n        </el-form-item>\n        <el-form-item label=\"库存数量\">\n          <el-input-number v-model=\"bookForm.stock\" :min=\"0\" />\n        </el-form-item>\n      </el-form>\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"dialogVisible = false\">取消</el-button>\n          <el-button type=\"primary\" @click=\"saveBook\">确定</el-button>\n        </span>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { ref, computed } from 'vue'\nimport { useStore } from 'vuex'\nimport { ElMessageBox, ElMessage } from 'element-plus'\nimport { Search } from '@element-plus/icons-vue'\n\nexport default {\n  name: 'BooksView',\n  components: {\n    Search\n  },\n  setup() {\n    const store = useStore()\n    const dialogVisible = ref(false)\n    const isEditing = ref(false)\n    const searchQuery = ref('')\n    \n    const bookForm = ref({\n      id: null,\n      title: '',\n      author: '',\n      category: '',\n      publisher: '',\n      isbn: '',\n      stock: 1,\n      borrowed: 0\n    })\n    \n    const books = computed(() => store.state.books)\n    \n    const filteredBooks = computed(() => {\n      if (!searchQuery.value) return books.value\n      \n      const query = searchQuery.value.toLowerCase()\n      return books.value.filter(book => \n        book.title.toLowerCase().includes(query) ||\n        book.author.toLowerCase().includes(query) ||\n        book.category.toLowerCase().includes(query) ||\n        book.isbn.toLowerCase().includes(query)\n      )\n    })\n    \n    const resetForm = () => {\n      bookForm.value = {\n        id: null,\n        title: '',\n        author: '',\n        category: '',\n        publisher: '',\n        isbn: '',\n        stock: 1,\n        borrowed: 0\n      }\n    }\n    \n    const editBook = (book) => {\n      isEditing.value = true\n      bookForm.value = { ...book }\n      dialogVisible.value = true\n    }\n    \n    const saveBook = () => {\n      if (isEditing.value) {\n        store.dispatch('updateBook', bookForm.value)\n        ElMessage.success('图书更新成功')\n      } else {\n        store.dispatch('addBook', bookForm.value)\n        ElMessage.success('图书添加成功')\n      }\n      \n      dialogVisible.value = false\n      resetForm()\n      isEditing.value = false\n    }\n    \n    const confirmDelete = (bookId) => {\n      ElMessageBox.confirm(\n        '确定要删除这本图书吗？此操作不可逆。',\n        '警告',\n        {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning',\n        }\n      )\n        .then(() => {\n          store.dispatch('deleteBook', bookId)\n          ElMessage.success('图书删除成功')\n        })\n        .catch(() => {\n          ElMessage.info('已取消删除')\n        })\n    }\n    \n    return {\n      dialogVisible,\n      isEditing,\n      bookForm,\n      books,\n      filteredBooks,\n      searchQuery,\n      editBook,\n      saveBook,\n      confirmDelete\n    }\n  }\n}\n</script>\n\n<style scoped>\n.books-container {\n  padding: 20px;\n}\n\n.action-bar {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 20px;\n}\n\n.search-input {\n  width: 300px;\n}\n</style>\n", "import { render } from \"./Books.vue?vue&type=template&id=290f36eb&scoped=true\"\nimport script from \"./Books.vue?vue&type=script&lang=js\"\nexport * from \"./Books.vue?vue&type=script&lang=js\"\n\nimport \"./Books.vue?vue&type=style&index=0&id=290f36eb&scoped=true&lang=css\"\n\nimport exportComponent from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\软件架构大作业\\\\homework\\\\node_modules\\\\vue-loader-v16\\\\dist\\\\exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-290f36eb\"]])\n\nexport default __exports__", "export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader-v16/dist/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader-v16/dist/index.js??ref--1-1!./Books.vue?vue&type=style&index=0&id=290f36eb&scoped=true&lang=css\""], "sourceRoot": ""}