{"version": 3, "sources": ["webpack:///webpack/bootstrap", "webpack:///./src/App.vue", "webpack:///./src/App.vue?8ecf", "webpack:///./src/router/index.js", "webpack:///./src/store/modules/auth.js", "webpack:///./src/store/index.js", "webpack:///./src/main.js", "webpack:///./src/assets/logo.png", "webpack:///./src/App.vue?e7b6"], "names": ["webpackJsonpCallback", "data", "moduleId", "chunkId", "chunkIds", "moreModules", "executeModules", "i", "resolves", "length", "Object", "prototype", "hasOwnProperty", "call", "installedChunks", "push", "modules", "parentJsonpFunction", "shift", "deferredModules", "apply", "checkDeferredModules", "result", "deferredModule", "fulfilled", "j", "depId", "splice", "__webpack_require__", "s", "installedModules", "installedCssChunks", "jsonpScriptSrc", "p", "exports", "module", "l", "e", "promises", "cssChunks", "Promise", "resolve", "reject", "href", "fullhref", "existingLinkTags", "document", "getElementsByTagName", "tag", "dataHref", "getAttribute", "rel", "existingStyleTags", "linkTag", "createElement", "type", "onload", "onerror", "event", "request", "target", "src", "err", "Error", "code", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "head", "append<PERSON><PERSON><PERSON>", "then", "installedChunkData", "promise", "onScriptComplete", "script", "charset", "timeout", "nc", "setAttribute", "error", "clearTimeout", "chunk", "errorType", "realSrc", "message", "name", "undefined", "setTimeout", "all", "m", "c", "d", "getter", "o", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "oe", "console", "jsonpArray", "window", "oldJsonpFunction", "slice", "id", "class", "_createElementBlock", "_hoisted_1", "$setup", "route", "path", "startsWith", "_createBlock", "_component_router_view", "_component_el_container", "isLoggedIn", "_component_el_aside", "width", "_createElementVNode", "alt", "_imports_0", "_createVNode", "_component_el_menu", "router", "default-active", "activeIndex", "background-color", "text-color", "active-text-color", "_component_el_menu_item", "index", "_component_el_icon", "_component_home", "_component_el_header", "_hoisted_2", "_toDisplayString", "pageTitle", "_hoisted_3", "currentUser", "username", "_component_el_button", "onClick", "logout", "_cache", "_component_el_main", "_component_el_footer", "Date", "getFullYear", "components", "Home", "setup", "useRoute", "useRouter", "computed", "localStorage", "getItem", "userStr", "JSON", "parse", "removeItem", "ElMessage", "success", "__exports__", "render", "routes", "component", "meta", "requiresAuth", "beforeEnter", "_", "__", "next", "user", "role", "requiresAdmin", "children", "createRouter", "history", "createWebHashHistory", "beforeEach", "to", "matched", "some", "record", "state", "isAuthenticated", "getters", "actions", "login", "commit", "userData", "users", "password", "foundUser", "find", "setItem", "stringify", "register", "userInput", "userExists", "log", "mutations", "SET_USER", "SET_AUTHENTICATED", "namespaced", "sampleBooks", "title", "author", "category", "publisher", "isbn", "stock", "borrowed", "sampleUsers", "contact", "borrowedBooks", "sampleBorrowings", "bookId", "userId", "borrowDate", "returnDate", "createStore", "books", "borrowings", "getBookById", "book", "getUserById", "getBorrowingsByUserId", "filter", "borrowing", "getBorrowingsByBookId", "getBooksByCategory", "categories", "for<PERSON>ach", "getBorrowingStatistics", "statistics", "b", "addBook", "updateBook", "updatedBook", "findIndex", "deleteBook", "addUser", "updateUser", "updatedUser", "deleteUser", "addBorrowing", "includes", "returnBook", "borrowingId", "toISOString", "split", "now", "borrowBook", "auth", "app", "createApp", "App", "entries", "ElementPlusIconsVue", "use", "store", "ElementPlus", "mount"], "mappings": "aACE,SAASA,EAAqBC,GAQ7B,IAPA,IAMIC,EAAUC,EANVC,EAAWH,EAAK,GAChBI,EAAcJ,EAAK,GACnBK,EAAiBL,EAAK,GAIHM,EAAI,EAAGC,EAAW,GACpCD,EAAIH,EAASK,OAAQF,IACzBJ,EAAUC,EAASG,GAChBG,OAAOC,UAAUC,eAAeC,KAAKC,EAAiBX,IAAYW,EAAgBX,IACpFK,EAASO,KAAKD,EAAgBX,GAAS,IAExCW,EAAgBX,GAAW,EAE5B,IAAID,KAAYG,EACZK,OAAOC,UAAUC,eAAeC,KAAKR,EAAaH,KACpDc,EAAQd,GAAYG,EAAYH,IAG/Be,GAAqBA,EAAoBhB,GAE5C,MAAMO,EAASC,OACdD,EAASU,OAATV,GAOD,OAHAW,EAAgBJ,KAAKK,MAAMD,EAAiBb,GAAkB,IAGvDe,IAER,SAASA,IAER,IADA,IAAIC,EACIf,EAAI,EAAGA,EAAIY,EAAgBV,OAAQF,IAAK,CAG/C,IAFA,IAAIgB,EAAiBJ,EAAgBZ,GACjCiB,GAAY,EACRC,EAAI,EAAGA,EAAIF,EAAed,OAAQgB,IAAK,CAC9C,IAAIC,EAAQH,EAAeE,GACG,IAA3BX,EAAgBY,KAAcF,GAAY,GAE3CA,IACFL,EAAgBQ,OAAOpB,IAAK,GAC5Be,EAASM,EAAoBA,EAAoBC,EAAIN,EAAe,KAItE,OAAOD,EAIR,IAAIQ,EAAmB,GAGnBC,EAAqB,CACxB,IAAO,GAMJjB,EAAkB,CACrB,IAAO,GAGJK,EAAkB,GAGtB,SAASa,EAAe7B,GACvB,OAAOyB,EAAoBK,EAAI,OAAS,GAAG9B,IAAUA,GAAW,IAAM,CAAC,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,YAAYA,GAAW,MAIxY,SAASyB,EAAoB1B,GAG5B,GAAG4B,EAAiB5B,GACnB,OAAO4B,EAAiB5B,GAAUgC,QAGnC,IAAIC,EAASL,EAAiB5B,GAAY,CACzCK,EAAGL,EACHkC,GAAG,EACHF,QAAS,IAUV,OANAlB,EAAQd,GAAUW,KAAKsB,EAAOD,QAASC,EAAQA,EAAOD,QAASN,GAG/DO,EAAOC,GAAI,EAGJD,EAAOD,QAKfN,EAAoBS,EAAI,SAAuBlC,GAC9C,IAAImC,EAAW,GAIXC,EAAY,CAAC,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,GAC7NR,EAAmB5B,GAAUmC,EAASvB,KAAKgB,EAAmB5B,IACzB,IAAhC4B,EAAmB5B,IAAkBoC,EAAUpC,IACtDmC,EAASvB,KAAKgB,EAAmB5B,GAAW,IAAIqC,SAAQ,SAASC,EAASC,GAIzE,IAHA,IAAIC,EAAO,QAAU,GAAGxC,IAAUA,GAAW,IAAM,CAAC,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,WAAW,iBAAiB,YAAYA,GAAW,OAChXyC,EAAWhB,EAAoBK,EAAIU,EACnCE,EAAmBC,SAASC,qBAAqB,QAC7CxC,EAAI,EAAGA,EAAIsC,EAAiBpC,OAAQF,IAAK,CAChD,IAAIyC,EAAMH,EAAiBtC,GACvB0C,EAAWD,EAAIE,aAAa,cAAgBF,EAAIE,aAAa,QACjE,GAAe,eAAZF,EAAIG,MAAyBF,IAAaN,GAAQM,IAAaL,GAAW,OAAOH,IAErF,IAAIW,EAAoBN,SAASC,qBAAqB,SACtD,IAAQxC,EAAI,EAAGA,EAAI6C,EAAkB3C,OAAQF,IAAK,CAC7CyC,EAAMI,EAAkB7C,GACxB0C,EAAWD,EAAIE,aAAa,aAChC,GAAGD,IAAaN,GAAQM,IAAaL,EAAU,OAAOH,IAEvD,IAAIY,EAAUP,SAASQ,cAAc,QACrCD,EAAQF,IAAM,aACdE,EAAQE,KAAO,WACfF,EAAQG,OAASf,EACjBY,EAAQI,QAAU,SAASC,GAC1B,IAAIC,EAAUD,GAASA,EAAME,QAAUF,EAAME,OAAOC,KAAOjB,EACvDkB,EAAM,IAAIC,MAAM,qBAAuB5D,EAAU,cAAgBwD,EAAU,KAC/EG,EAAIE,KAAO,wBACXF,EAAIH,QAAUA,SACP5B,EAAmB5B,GAC1BkD,EAAQY,WAAWC,YAAYb,GAC/BX,EAAOoB,IAERT,EAAQV,KAAOC,EAEf,IAAIuB,EAAOrB,SAASC,qBAAqB,QAAQ,GACjDoB,EAAKC,YAAYf,MACfgB,MAAK,WACPtC,EAAmB5B,GAAW,MAMhC,IAAImE,EAAqBxD,EAAgBX,GACzC,GAA0B,IAAvBmE,EAGF,GAAGA,EACFhC,EAASvB,KAAKuD,EAAmB,QAC3B,CAEN,IAAIC,EAAU,IAAI/B,SAAQ,SAASC,EAASC,GAC3C4B,EAAqBxD,EAAgBX,GAAW,CAACsC,EAASC,MAE3DJ,EAASvB,KAAKuD,EAAmB,GAAKC,GAGtC,IACIC,EADAC,EAAS3B,SAASQ,cAAc,UAGpCmB,EAAOC,QAAU,QACjBD,EAAOE,QAAU,IACb/C,EAAoBgD,IACvBH,EAAOI,aAAa,QAASjD,EAAoBgD,IAElDH,EAAOZ,IAAM7B,EAAe7B,GAG5B,IAAI2E,EAAQ,IAAIf,MAChBS,EAAmB,SAAUd,GAE5Be,EAAOhB,QAAUgB,EAAOjB,OAAS,KACjCuB,aAAaJ,GACb,IAAIK,EAAQlE,EAAgBX,GAC5B,GAAa,IAAV6E,EAAa,CACf,GAAGA,EAAO,CACT,IAAIC,EAAYvB,IAAyB,SAAfA,EAAMH,KAAkB,UAAYG,EAAMH,MAChE2B,EAAUxB,GAASA,EAAME,QAAUF,EAAME,OAAOC,IACpDiB,EAAMK,QAAU,iBAAmBhF,EAAU,cAAgB8E,EAAY,KAAOC,EAAU,IAC1FJ,EAAMM,KAAO,iBACbN,EAAMvB,KAAO0B,EACbH,EAAMnB,QAAUuB,EAChBF,EAAM,GAAGF,GAEVhE,EAAgBX,QAAWkF,IAG7B,IAAIV,EAAUW,YAAW,WACxBd,EAAiB,CAAEjB,KAAM,UAAWK,OAAQa,MAC1C,MACHA,EAAOhB,QAAUgB,EAAOjB,OAASgB,EACjC1B,SAASqB,KAAKC,YAAYK,GAG5B,OAAOjC,QAAQ+C,IAAIjD,IAIpBV,EAAoB4D,EAAIxE,EAGxBY,EAAoB6D,EAAI3D,EAGxBF,EAAoB8D,EAAI,SAASxD,EAASkD,EAAMO,GAC3C/D,EAAoBgE,EAAE1D,EAASkD,IAClC1E,OAAOmF,eAAe3D,EAASkD,EAAM,CAAEU,YAAY,EAAMC,IAAKJ,KAKhE/D,EAAoBoE,EAAI,SAAS9D,GACX,qBAAX+D,QAA0BA,OAAOC,aAC1CxF,OAAOmF,eAAe3D,EAAS+D,OAAOC,YAAa,CAAEC,MAAO,WAE7DzF,OAAOmF,eAAe3D,EAAS,aAAc,CAAEiE,OAAO,KAQvDvE,EAAoBwE,EAAI,SAASD,EAAOE,GAEvC,GADU,EAAPA,IAAUF,EAAQvE,EAAoBuE,IAC/B,EAAPE,EAAU,OAAOF,EACpB,GAAW,EAAPE,GAA8B,kBAAVF,GAAsBA,GAASA,EAAMG,WAAY,OAAOH,EAChF,IAAII,EAAK7F,OAAO8F,OAAO,MAGvB,GAFA5E,EAAoBoE,EAAEO,GACtB7F,OAAOmF,eAAeU,EAAI,UAAW,CAAET,YAAY,EAAMK,MAAOA,IACtD,EAAPE,GAA4B,iBAATF,EAAmB,IAAI,IAAIM,KAAON,EAAOvE,EAAoB8D,EAAEa,EAAIE,EAAK,SAASA,GAAO,OAAON,EAAMM,IAAQC,KAAK,KAAMD,IAC9I,OAAOF,GAIR3E,EAAoB+E,EAAI,SAASxE,GAChC,IAAIwD,EAASxD,GAAUA,EAAOmE,WAC7B,WAAwB,OAAOnE,EAAO,YACtC,WAA8B,OAAOA,GAEtC,OADAP,EAAoB8D,EAAEC,EAAQ,IAAKA,GAC5BA,GAIR/D,EAAoBgE,EAAI,SAASgB,EAAQC,GAAY,OAAOnG,OAAOC,UAAUC,eAAeC,KAAK+F,EAAQC,IAGzGjF,EAAoBK,EAAI,IAGxBL,EAAoBkF,GAAK,SAAShD,GAA2B,MAApBiD,QAAQjC,MAAMhB,GAAYA,GAEnE,IAAIkD,EAAaC,OAAO,gBAAkBA,OAAO,iBAAmB,GAChEC,EAAmBF,EAAWjG,KAAK2F,KAAKM,GAC5CA,EAAWjG,KAAOf,EAClBgH,EAAaA,EAAWG,QACxB,IAAI,IAAI5G,EAAI,EAAGA,EAAIyG,EAAWvG,OAAQF,IAAKP,EAAqBgH,EAAWzG,IAC3E,IAAIU,EAAsBiG,EAI1B/F,EAAgBJ,KAAK,CAAC,EAAE,kBAEjBM,K,kICzQF+F,GAAG,O,GA2BKC,MAAM,kB,GAEJA,MAAM,a,yiBA7BrBC,gCA4CM,MA5CNC,EA4CM,CA1C8B,WAAfC,EAAAC,MAAMC,MAAqBF,EAAAC,MAAMC,KAAKC,WAAW,UAAYH,EAAAC,MAAMC,KAAKC,WAAW,W,yBAAtGC,yBAAmHC,EAAA,CAAApB,IAAA,M,yBAGnHmB,yBAsCeE,EAAA,CAAArB,IAAA,I,6BArCb,IAkBW,CAlBmBe,EAAAO,Y,yBAA9BH,yBAkBWI,EAAA,C,MAlBDC,MAAM,S,8BACd,IAGM,C,YAHNC,gCAGM,OAHDb,MAAM,kBAAgB,CACzBa,gCAAyD,OAApDC,IAAI,WAAWtE,IAAAuE,IAAwBf,MAAM,SAClDa,gCAAe,UAAX,Y,IAENG,yBAYUC,EAAA,CAXRC,OAAA,GACCC,iBAAgBhB,EAAAiB,YACjBpB,MAAM,mBACNqB,mBAAiB,UACjBC,aAAW,OACXC,oBAAkB,W,8BAElB,IAGe,CAHfP,yBAGeQ,EAAA,CAHDC,MAAM,KAAG,C,6BACrB,IAA2B,CAA3BT,yBAA2BU,EAAA,M,6BAAlB,IAAQ,CAARV,yBAAQW,K,kBACjBd,gCAAe,YAAT,MAAE,M,iGAIdG,yBAiBeP,EAAA,M,6BAhBb,IAQY,CARKN,EAAAO,Y,yBAAjBH,yBAQYqB,EAAA,CAAAxC,IAAA,I,6BAPV,IAMM,CANNyB,gCAMM,MANNgB,EAMM,CALJhB,gCAAwB,UAAAiB,6BAAjB3B,EAAA4B,WAAS,GAChBlB,gCAGM,MAHNmB,EAGM,CAFJnB,gCAA0C,YAApC,MAAGiB,6BAAG3B,EAAA8B,YAAYC,UAAQ,GAChClB,yBAAuDmB,EAAA,CAA5CjG,KAAK,OAAQkG,QAAOjC,EAAAkC,Q,8BAAQ,IAAIC,EAAA,KAAAA,EAAA,I,6BAAJ,W,+EAI7CtB,yBAGUuB,EAAA,M,6BADR,IAAyC,CAAP,MAAfpC,EAAAC,MAAMC,M,yBAAzBE,yBAAyCC,EAAA,CAAApB,IAAA,K,+CAE1Be,EAAAO,Y,yBAAjBH,yBAEYiC,EAAA,CAAApD,IAAA,I,6BADV,IAAmD,CAAnDyB,gCAAmD,SAAhD,YAAciB,8BAAA,IAAOW,MAAOC,eAAW,K,mHAerC,GACb3E,KAAM,MACN4E,WAAY,CACVC,sBAEFC,QACE,MAAMzC,EAAQ0C,iBACR5B,EAAS6B,iBAET3B,EAAc4B,sBAAS,IAAM5C,EAAMC,MAGnCK,EAAasC,sBAAS,IACc,OAAjCC,aAAaC,QAAQ,SAIxBjB,EAAce,sBAAS,KAC3B,MAAMG,EAAUF,aAAaC,QAAQ,QACrC,OAAOC,EAAUC,KAAKC,MAAMF,GAAW,CAAEjB,SAAU,MAG/CH,EAAYiB,sBAAS,KACzB,OAAQ5C,EAAMC,MACZ,IAAK,IACH,MAAO,OACT,IAAK,SACH,MAAO,QACT,QACE,MAAO,YAKPgC,EAASA,KACbY,aAAaK,WAAW,QACxBC,OAAUC,QAAQ,SAClBtC,EAAOxH,KAAK,WAGd,MAAO,CACL0G,QACAgB,cACAW,YACArB,aACAuB,cACAI,Y,iCC/FN,MAAMoB,EAA2B,IAAgB,EAAQ,CAAC,CAAC,SAASC,KAErD,Q,oBCPf,MAAMC,EAAS,CACb,CACEtD,KAAM,IACNtC,KAAM,OACN6F,UAAWA,IAAM,gDACjBC,KAAM,CAAEC,cAAc,GACtBC,YAAaA,CAACC,EAAGC,EAAIC,KACnB,MAAMf,EAAUF,aAAaC,QAAQ,QAC/BiB,EAAOhB,EAAUC,KAAKC,MAAMF,GAAW,KAEzCgB,EACgB,UAAdA,EAAKC,KACPF,EAAK,CAAE7D,KAAM,iBAEb6D,EAAK,CAAE7D,KAAM,gBAGf6D,EAAK,CAAE7D,KAAM,aAKnB,CACEA,KAAM,SACNtC,KAAM,QACN6F,UAAWA,IAAM,gDACjBC,KAAM,CAAEC,cAAc,EAAMO,eAAe,GAC3CC,SAAU,CACR,CACEjE,KAAM,QACNtC,KAAM,aACN6F,UAAWA,IAAM,iDAEnB,CACEvD,KAAM,QACNtC,KAAM,aACN6F,UAAWA,IAAM,iDAEnB,CACEvD,KAAM,YACNtC,KAAM,iBACN6F,UAAWA,IAAM,iDAEnB,CACEvD,KAAM,aACNtC,KAAM,kBACN6F,UAAWA,IAAM,mDAKvB,CACEvD,KAAM,QACNtC,KAAM,WACN6F,UAAWA,IAAM,gDACjBC,KAAM,CAAEC,cAAc,GACtBQ,SAAU,CACR,CACEjE,KAAM,QACNtC,KAAM,YACN6F,UAAWA,IAAM,iDAEnB,CACEvD,KAAM,YACNtC,KAAM,gBACN6F,UAAWA,IAAM,iDAEnB,CACEvD,KAAM,UACNtC,KAAM,cACN6F,UAAWA,IAAM,mDAIvB,CACEvD,KAAM,SACNtC,KAAM,QACN6F,UAAWA,IAAM,kDAIf1C,EAASqD,eAAa,CAC1BC,QAASC,iBACTd,WAIFzC,EAAOwD,WAAW,CAACC,EAAIX,EAAGE,KACxB,MAAMf,EAAUF,aAAaC,QAAQ,QAC/BiB,EAAOhB,EAAUC,KAAKC,MAAMF,GAAW,KAEzCwB,EAAGC,QAAQC,KAAKC,GAAUA,EAAOjB,KAAKC,cAEnCK,EAGMQ,EAAGC,QAAQC,KAAKC,GAAUA,EAAOjB,KAAKQ,gBAAgC,UAAdF,EAAKC,KAEtEF,EAAK,CAAE7D,KAAM,gBAGb6D,IANAA,EAAK,CAAEnG,KAAM,UAUfmG,MAIWhD,Q,sDC5Gf,MAAM6D,EAAQ,CACZZ,KAAMf,KAAKC,MAAMJ,aAAaC,QAAQ,UAAY,KAClD8B,kBAAmB/B,aAAaC,QAAQ,SAIpC+B,EAAU,CACdD,gBAAiBD,GAASA,EAAMC,gBAChC/C,YAAa8C,GAASA,EAAMZ,MAIxBe,EAAU,CAEdC,OAAM,OAAEC,GAAUC,GAChB,OAAO,IAAIlK,QAAQ,CAACC,EAASC,KAC3B,IAKE,MAAMiK,EAAQ,CACZ,CAAEpD,SAAU,QAASqD,SAAU,WAAYnB,KAAM,SACjD,CAAElC,SAAU,OAAQqD,SAAU,UAAWnB,KAAM,QAC/C,CAAElC,SAAU,OAAQqD,SAAU,UAAWnB,KAAM,SAI3CoB,EAAYF,EAAMG,KAAKtB,GAC3BA,EAAKjC,WAAamD,EAASnD,UAC3BiC,EAAKoB,WAAaF,EAASE,UAG7B,IAAKC,EACH,MAAM,IAAI9I,MAAM,YAGlB,MAAMyH,EAAO,CACXjC,SAAUsD,EAAUtD,SACpBkC,KAAMoB,EAAUpB,MAIlBnB,aAAayC,QAAQ,OAAQtC,KAAKuC,UAAUxB,IAG5CiB,EAAO,WAAYjB,GACnBiB,EAAO,qBAAqB,GAE5BhK,EAAQ+I,GACR,MAAO1G,GACPpC,EAAOoC,OAMbmI,SAAS5B,EAAG6B,GACV,OAAO,IAAI1K,QAAQ,CAACC,EAASC,KAC3B,IASE,MAAMiK,EAAQ,CACZ,CAAEpD,SAAU,QAASqD,SAAU,WAAYnB,KAAM,SACjD,CAAElC,SAAU,OAAQqD,SAAU,UAAWnB,KAAM,QAC/C,CAAElC,SAAU,OAAQqD,SAAU,UAAWnB,KAAM,SAG3C0B,EAAaR,EAAMT,KAAKV,GAAQA,EAAKjC,WAAa2D,EAAU3D,UAElE,GAAI4D,EACF,MAAM,IAAIpJ,MAAM,UAIlBgD,QAAQqG,IAAI,QAASF,EAAU3D,SAAU,MAAO2D,EAAUN,UAC1DnK,EAAQ,CAAEoI,SAAS,IACnB,MAAO/F,GACPpC,EAAOoC,OAMb4E,QAAO,OAAE+C,IAQP,OANAnC,aAAaK,WAAW,QAGxB8B,EAAO,WAAY,MACnBA,EAAO,qBAAqB,GAErBjK,QAAQC,YAKb4K,EAAY,CAChBC,SAASlB,EAAOZ,GACdY,EAAMZ,KAAOA,GAEf+B,kBAAkBnB,EAAOC,GACvBD,EAAMC,gBAAkBA,IAIb,OACbmB,YAAY,EACZpB,QACAE,UACAC,UACAc,aCpHF,MAAMI,EAAc,CAClB,CAAErG,GAAI,EAAGsG,MAAO,KAAMC,OAAQ,MAAOC,SAAU,KAAMC,UAAW,QAASC,KAAM,gBAAiBC,MAAO,GAAIC,SAAU,GACrH,CAAE5G,GAAI,EAAGsG,MAAO,KAAMC,OAAQ,KAAMC,SAAU,KAAMC,UAAW,QAASC,KAAM,gBAAiBC,MAAO,EAAGC,SAAU,GACnH,CAAE5G,GAAI,EAAGsG,MAAO,OAAQC,OAAQ,WAAYC,SAAU,KAAMC,UAAW,SAAUC,KAAM,gBAAiBC,MAAO,EAAGC,SAAU,GAC5H,CAAE5G,GAAI,EAAGsG,MAAO,OAAQC,OAAQ,mBAAoBC,SAAU,MAAOC,UAAW,UAAWC,KAAM,gBAAiBC,MAAO,EAAGC,SAAU,GACtI,CAAE5G,GAAI,EAAGsG,MAAO,OAAQC,OAAQ,UAAWC,SAAU,KAAMC,UAAW,QAASC,KAAM,gBAAiBC,MAAO,EAAGC,SAAU,IAGtHC,EAAc,CAClB,CAAE7G,GAAI,EAAGhC,KAAM,KAAM8I,QAAS,cAAeC,cAAe,CAAC,EAAG,IAChE,CAAE/G,GAAI,EAAGhC,KAAM,KAAM8I,QAAS,cAAeC,cAAe,CAAC,IAC7D,CAAE/G,GAAI,EAAGhC,KAAM,KAAM8I,QAAS,cAAeC,cAAe,CAAC,EAAG,KAG5DC,EAAmB,CACvB,CAAEhH,GAAI,EAAGiH,OAAQ,EAAGC,OAAQ,EAAGC,WAAY,aAAcC,WAAY,MACrE,CAAEpH,GAAI,EAAGiH,OAAQ,EAAGC,OAAQ,EAAGC,WAAY,aAAcC,WAAY,MACrE,CAAEpH,GAAI,EAAGiH,OAAQ,EAAGC,OAAQ,EAAGC,WAAY,aAAcC,WAAY,MACrE,CAAEpH,GAAI,EAAGiH,OAAQ,EAAGC,OAAQ,EAAGC,WAAY,aAAcC,WAAY,MACrE,CAAEpH,GAAI,EAAGiH,OAAQ,EAAGC,OAAQ,EAAGC,WAAY,aAAcC,WAAY,OAGxDC,qBAAY,CACzBrC,MAAO,CACLsC,MAAOjB,EACPd,MAAOsB,EACPU,WAAYP,GAEd9B,QAAS,CACPsC,YAAcxC,GAAWhF,GAChBgF,EAAMsC,MAAM5B,KAAK+B,GAAQA,EAAKzH,KAAOA,GAE9C0H,YAAc1C,GAAWhF,GAChBgF,EAAMO,MAAMG,KAAKtB,GAAQA,EAAKpE,KAAOA,GAE9C2H,sBAAwB3C,GAAWkC,GAC1BlC,EAAMuC,WAAWK,OAAOC,GAAaA,EAAUX,SAAWA,GAEnEY,sBAAwB9C,GAAWiC,GAC1BjC,EAAMuC,WAAWK,OAAOC,GAAaA,EAAUZ,SAAWA,GAEnEc,mBAAqB/C,IACnB,MAAMgD,EAAa,GAOnB,OANAhD,EAAMsC,MAAMW,QAAQR,IACbO,EAAWP,EAAKjB,YACnBwB,EAAWP,EAAKjB,UAAY,GAE9BwB,EAAWP,EAAKjB,cAEXwB,GAETE,uBAAyBlD,IACvB,MAAMmD,EAAa,GAUnB,OATAnD,EAAMuC,WAAWU,QAAQJ,IACvB,MAAMJ,EAAOzC,EAAMsC,MAAM5B,KAAK0C,GAAKA,EAAEpI,KAAO6H,EAAUZ,QAClDQ,IACGU,EAAWV,EAAKjB,YACnB2B,EAAWV,EAAKjB,UAAY,GAE9B2B,EAAWV,EAAKjB,eAGb2B,IAGXlC,UAAW,CACToC,QAAQrD,EAAOyC,GACbzC,EAAMsC,MAAM3N,KAAK8N,IAEnBa,WAAWtD,EAAOuD,GAChB,MAAM7G,EAAQsD,EAAMsC,MAAMkB,UAAUf,GAAQA,EAAKzH,KAAOuI,EAAYvI,KACrD,IAAX0B,IACFsD,EAAMsC,MAAM5F,GAAS6G,IAGzBE,WAAWzD,EAAOiC,GAChBjC,EAAMsC,MAAQtC,EAAMsC,MAAMM,OAAOH,GAAQA,EAAKzH,KAAOiH,IAEvDyB,QAAQ1D,EAAOZ,GACbY,EAAMO,MAAM5L,KAAKyK,IAEnBuE,WAAW3D,EAAO4D,GAChB,MAAMlH,EAAQsD,EAAMO,MAAMiD,UAAUpE,GAAQA,EAAKpE,KAAO4I,EAAY5I,KACrD,IAAX0B,IACFsD,EAAMO,MAAM7D,GAASkH,IAGzBC,WAAW7D,EAAOkC,GAChBlC,EAAMO,MAAQP,EAAMO,MAAMqC,OAAOxD,GAAQA,EAAKpE,KAAOkH,IAEvD4B,aAAa9D,EAAO6C,GAClB7C,EAAMuC,WAAW5N,KAAKkO,GAEtB,MAAMJ,EAAOzC,EAAMsC,MAAM5B,KAAK+B,GAAQA,EAAKzH,KAAO6H,EAAUZ,QACxDQ,GACFA,EAAKb,WAGP,MAAMxC,EAAOY,EAAMO,MAAMG,KAAKtB,GAAQA,EAAKpE,KAAO6H,EAAUX,QACxD9C,IAASA,EAAK2C,cAAcgC,SAASlB,EAAUZ,SACjD7C,EAAK2C,cAAcpN,KAAKkO,EAAUZ,SAGtC+B,WAAWhE,EAAOiE,GAChB,MAAMpB,EAAY7C,EAAMuC,WAAW7B,KAAK0C,GAAKA,EAAEpI,KAAOiJ,GACtD,GAAIpB,EAAW,CACbA,EAAUT,YAAa,IAAI1E,MAAOwG,cAAcC,MAAM,KAAK,GAE3D,MAAM1B,EAAOzC,EAAMsC,MAAM5B,KAAK+B,GAAQA,EAAKzH,KAAO6H,EAAUZ,QACxDQ,GACFA,EAAKb,WAGP,MAAMxC,EAAOY,EAAMO,MAAMG,KAAKtB,GAAQA,EAAKpE,KAAO6H,EAAUX,QACxD9C,IACFA,EAAK2C,cAAgB3C,EAAK2C,cAAca,OAAO5H,GAAMA,IAAO6H,EAAUZ,YAK9E9B,QAAS,CACPkD,SAAQ,OAAEhD,GAAUoC,GAClBpC,EAAO,UAAW,IACboC,EACHzH,GAAI0C,KAAK0G,MACTxC,SAAU,KAGd0B,YAAW,OAAEjD,GAAUoC,GACrBpC,EAAO,aAAcoC,IAEvBgB,YAAW,OAAEpD,GAAU4B,GACrB5B,EAAO,aAAc4B,IAEvByB,SAAQ,OAAErD,GAAUjB,GAClBiB,EAAO,UAAW,IACbjB,EACHpE,GAAI0C,KAAK0G,MACTrC,cAAe,MAGnB4B,YAAW,OAAEtD,GAAUjB,GACrBiB,EAAO,aAAcjB,IAEvByE,YAAW,OAAExD,GAAU6B,GACrB7B,EAAO,aAAc6B,IAEvBmC,YAAW,OAAEhE,EAAM,MAAEL,IAAS,OAAEiC,EAAM,OAAEC,IACtC,MAAMO,EAAOzC,EAAMsC,MAAM5B,KAAK0C,GAAKA,EAAEpI,KAAOiH,GAC5C,SAAIQ,GAAQA,EAAKd,MAAQc,EAAKb,YAC5BvB,EAAO,eAAgB,CACrBrF,GAAI0C,KAAK0G,MACTnC,SACAC,SACAC,YAAY,IAAIzE,MAAOwG,cAAcC,MAAM,KAAK,GAChD/B,WAAY,QAEP,IAIX4B,YAAW,OAAE3D,GAAU4D,GACrB5D,EAAO,aAAc4D,KAGzBrP,QAAS,CACP0P,U,sBClKJ,MAAMC,EAAMC,uBAAUC,GAGtB,IAAK,MAAOpK,EAAKwE,KAAcvK,OAAOoQ,QAAQC,GAC5CJ,EAAI1F,UAAUxE,EAAKwE,GAGrB0F,EAAIK,IAAIzI,GACRoI,EAAIK,IAAIC,GACRN,EAAIK,IAAIE,QAERP,EAAIQ,MAAM,S,qBCnBVhP,EAAOD,QAAU,IAA0B,yB,yDCA3C", "file": "js/app.d4e0f07f.js", "sourcesContent": [" \t// install a JSONP callback for chunk loading\n \tfunction webpackJsonpCallback(data) {\n \t\tvar chunkIds = data[0];\n \t\tvar moreModules = data[1];\n \t\tvar executeModules = data[2];\n\n \t\t// add \"moreModules\" to the modules object,\n \t\t// then flag all \"chunkIds\" as loaded and fire callback\n \t\tvar moduleId, chunkId, i = 0, resolves = [];\n \t\tfor(;i < chunkIds.length; i++) {\n \t\t\tchunkId = chunkIds[i];\n \t\t\tif(Object.prototype.hasOwnProperty.call(installedChunks, chunkId) && installedChunks[chunkId]) {\n \t\t\t\tresolves.push(installedChunks[chunkId][0]);\n \t\t\t}\n \t\t\tinstalledChunks[chunkId] = 0;\n \t\t}\n \t\tfor(moduleId in moreModules) {\n \t\t\tif(Object.prototype.hasOwnProperty.call(moreModules, moduleId)) {\n \t\t\t\tmodules[moduleId] = moreModules[moduleId];\n \t\t\t}\n \t\t}\n \t\tif(parentJsonpFunction) parentJsonpFunction(data);\n\n \t\twhile(resolves.length) {\n \t\t\tresolves.shift()();\n \t\t}\n\n \t\t// add entry modules from loaded chunk to deferred list\n \t\tdeferredModules.push.apply(deferredModules, executeModules || []);\n\n \t\t// run deferred modules when all chunks ready\n \t\treturn checkDeferredModules();\n \t};\n \tfunction checkDeferredModules() {\n \t\tvar result;\n \t\tfor(var i = 0; i < deferredModules.length; i++) {\n \t\t\tvar deferredModule = deferredModules[i];\n \t\t\tvar fulfilled = true;\n \t\t\tfor(var j = 1; j < deferredModule.length; j++) {\n \t\t\t\tvar depId = deferredModule[j];\n \t\t\t\tif(installedChunks[depId] !== 0) fulfilled = false;\n \t\t\t}\n \t\t\tif(fulfilled) {\n \t\t\t\tdeferredModules.splice(i--, 1);\n \t\t\t\tresult = __webpack_require__(__webpack_require__.s = deferredModule[0]);\n \t\t\t}\n \t\t}\n\n \t\treturn result;\n \t}\n\n \t// The module cache\n \tvar installedModules = {};\n\n \t// object to store loaded CSS chunks\n \tvar installedCssChunks = {\n \t\t\"app\": 0\n \t}\n\n \t// object to store loaded and loading chunks\n \t// undefined = chunk not loaded, null = chunk preloaded/prefetched\n \t// Promise = chunk loading, 0 = chunk loaded\n \tvar installedChunks = {\n \t\t\"app\": 0\n \t};\n\n \tvar deferredModules = [];\n\n \t// script path function\n \tfunction jsonpScriptSrc(chunkId) {\n \t\treturn __webpack_require__.p + \"js/\" + ({}[chunkId]||chunkId) + \".\" + {\"chunk-00626164\":\"1577ca5b\",\"chunk-17cdc884\":\"f6b3981b\",\"chunk-1dea58d1\":\"97295288\",\"chunk-2b157610\":\"21c2e8d7\",\"chunk-397223cc\":\"00dd1c5c\",\"chunk-49c316d5\":\"d6fecff8\",\"chunk-52ea0bcc\":\"cce8b994\",\"chunk-5743ec8d\":\"61a0ec75\",\"chunk-6df680a9\":\"a868d046\",\"chunk-a1ab17f4\":\"f89343c4\",\"chunk-ec892d4a\":\"d34f040a\"}[chunkId] + \".js\"\n \t}\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n \t// This file contains only the entry chunk.\n \t// The chunk loading function for additional chunks\n \t__webpack_require__.e = function requireEnsure(chunkId) {\n \t\tvar promises = [];\n\n\n \t\t// mini-css-extract-plugin CSS loading\n \t\tvar cssChunks = {\"chunk-00626164\":1,\"chunk-17cdc884\":1,\"chunk-1dea58d1\":1,\"chunk-2b157610\":1,\"chunk-397223cc\":1,\"chunk-49c316d5\":1,\"chunk-52ea0bcc\":1,\"chunk-5743ec8d\":1,\"chunk-6df680a9\":1,\"chunk-a1ab17f4\":1,\"chunk-ec892d4a\":1};\n \t\tif(installedCssChunks[chunkId]) promises.push(installedCssChunks[chunkId]);\n \t\telse if(installedCssChunks[chunkId] !== 0 && cssChunks[chunkId]) {\n \t\t\tpromises.push(installedCssChunks[chunkId] = new Promise(function(resolve, reject) {\n \t\t\t\tvar href = \"css/\" + ({}[chunkId]||chunkId) + \".\" + {\"chunk-00626164\":\"5161fa98\",\"chunk-17cdc884\":\"4da3b921\",\"chunk-1dea58d1\":\"566c283a\",\"chunk-2b157610\":\"99b87d5c\",\"chunk-397223cc\":\"9bb09de2\",\"chunk-49c316d5\":\"fb5834f5\",\"chunk-52ea0bcc\":\"5910aac0\",\"chunk-5743ec8d\":\"d37425c6\",\"chunk-6df680a9\":\"319f9987\",\"chunk-a1ab17f4\":\"55ed3274\",\"chunk-ec892d4a\":\"5fe2aab8\"}[chunkId] + \".css\";\n \t\t\t\tvar fullhref = __webpack_require__.p + href;\n \t\t\t\tvar existingLinkTags = document.getElementsByTagName(\"link\");\n \t\t\t\tfor(var i = 0; i < existingLinkTags.length; i++) {\n \t\t\t\t\tvar tag = existingLinkTags[i];\n \t\t\t\t\tvar dataHref = tag.getAttribute(\"data-href\") || tag.getAttribute(\"href\");\n \t\t\t\t\tif(tag.rel === \"stylesheet\" && (dataHref === href || dataHref === fullhref)) return resolve();\n \t\t\t\t}\n \t\t\t\tvar existingStyleTags = document.getElementsByTagName(\"style\");\n \t\t\t\tfor(var i = 0; i < existingStyleTags.length; i++) {\n \t\t\t\t\tvar tag = existingStyleTags[i];\n \t\t\t\t\tvar dataHref = tag.getAttribute(\"data-href\");\n \t\t\t\t\tif(dataHref === href || dataHref === fullhref) return resolve();\n \t\t\t\t}\n \t\t\t\tvar linkTag = document.createElement(\"link\");\n \t\t\t\tlinkTag.rel = \"stylesheet\";\n \t\t\t\tlinkTag.type = \"text/css\";\n \t\t\t\tlinkTag.onload = resolve;\n \t\t\t\tlinkTag.onerror = function(event) {\n \t\t\t\t\tvar request = event && event.target && event.target.src || fullhref;\n \t\t\t\t\tvar err = new Error(\"Loading CSS chunk \" + chunkId + \" failed.\\n(\" + request + \")\");\n \t\t\t\t\terr.code = \"CSS_CHUNK_LOAD_FAILED\";\n \t\t\t\t\terr.request = request;\n \t\t\t\t\tdelete installedCssChunks[chunkId]\n \t\t\t\t\tlinkTag.parentNode.removeChild(linkTag)\n \t\t\t\t\treject(err);\n \t\t\t\t};\n \t\t\t\tlinkTag.href = fullhref;\n\n \t\t\t\tvar head = document.getElementsByTagName(\"head\")[0];\n \t\t\t\thead.appendChild(linkTag);\n \t\t\t}).then(function() {\n \t\t\t\tinstalledCssChunks[chunkId] = 0;\n \t\t\t}));\n \t\t}\n\n \t\t// JSONP chunk loading for javascript\n\n \t\tvar installedChunkData = installedChunks[chunkId];\n \t\tif(installedChunkData !== 0) { // 0 means \"already installed\".\n\n \t\t\t// a Promise means \"currently loading\".\n \t\t\tif(installedChunkData) {\n \t\t\t\tpromises.push(installedChunkData[2]);\n \t\t\t} else {\n \t\t\t\t// setup Promise in chunk cache\n \t\t\t\tvar promise = new Promise(function(resolve, reject) {\n \t\t\t\t\tinstalledChunkData = installedChunks[chunkId] = [resolve, reject];\n \t\t\t\t});\n \t\t\t\tpromises.push(installedChunkData[2] = promise);\n\n \t\t\t\t// start chunk loading\n \t\t\t\tvar script = document.createElement('script');\n \t\t\t\tvar onScriptComplete;\n\n \t\t\t\tscript.charset = 'utf-8';\n \t\t\t\tscript.timeout = 120;\n \t\t\t\tif (__webpack_require__.nc) {\n \t\t\t\t\tscript.setAttribute(\"nonce\", __webpack_require__.nc);\n \t\t\t\t}\n \t\t\t\tscript.src = jsonpScriptSrc(chunkId);\n\n \t\t\t\t// create error before stack unwound to get useful stacktrace later\n \t\t\t\tvar error = new Error();\n \t\t\t\tonScriptComplete = function (event) {\n \t\t\t\t\t// avoid mem leaks in IE.\n \t\t\t\t\tscript.onerror = script.onload = null;\n \t\t\t\t\tclearTimeout(timeout);\n \t\t\t\t\tvar chunk = installedChunks[chunkId];\n \t\t\t\t\tif(chunk !== 0) {\n \t\t\t\t\t\tif(chunk) {\n \t\t\t\t\t\t\tvar errorType = event && (event.type === 'load' ? 'missing' : event.type);\n \t\t\t\t\t\t\tvar realSrc = event && event.target && event.target.src;\n \t\t\t\t\t\t\terror.message = 'Loading chunk ' + chunkId + ' failed.\\n(' + errorType + ': ' + realSrc + ')';\n \t\t\t\t\t\t\terror.name = 'ChunkLoadError';\n \t\t\t\t\t\t\terror.type = errorType;\n \t\t\t\t\t\t\terror.request = realSrc;\n \t\t\t\t\t\t\tchunk[1](error);\n \t\t\t\t\t\t}\n \t\t\t\t\t\tinstalledChunks[chunkId] = undefined;\n \t\t\t\t\t}\n \t\t\t\t};\n \t\t\t\tvar timeout = setTimeout(function(){\n \t\t\t\t\tonScriptComplete({ type: 'timeout', target: script });\n \t\t\t\t}, 120000);\n \t\t\t\tscript.onerror = script.onload = onScriptComplete;\n \t\t\t\tdocument.head.appendChild(script);\n \t\t\t}\n \t\t}\n \t\treturn Promise.all(promises);\n \t};\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"/\";\n\n \t// on error function for async loading\n \t__webpack_require__.oe = function(err) { console.error(err); throw err; };\n\n \tvar jsonpArray = window[\"webpackJsonp\"] = window[\"webpackJsonp\"] || [];\n \tvar oldJsonpFunction = jsonpArray.push.bind(jsonpArray);\n \tjsonpArray.push = webpackJsonpCallback;\n \tjsonpArray = jsonpArray.slice();\n \tfor(var i = 0; i < jsonpArray.length; i++) webpackJsonpCallback(jsonpArray[i]);\n \tvar parentJsonpFunction = oldJsonpFunction;\n\n\n \t// add entry module to deferred list\n \tdeferredModules.push([0,\"chunk-vendors\"]);\n \t// run deferred modules when ready\n \treturn checkDeferredModules();\n", "<template>\n  <div id=\"app\">\n    <!-- 登录页面、用户路由和管理员路由直接显示对应的布局组件 -->\n    <router-view v-if=\"route.path === '/login' || route.path.startsWith('/user') || route.path.startsWith('/admin')\" />\n\n    <!-- 其他页面显示App.vue的布局 -->\n    <el-container v-else>\n      <el-aside width=\"200px\" v-if=\"isLoggedIn\">\n        <div class=\"logo-container\">\n          <img alt=\"Vue logo\" src=\"./assets/logo.png\" class=\"logo\">\n          <h3>图书管理系统</h3>\n        </div>\n        <el-menu\n          router\n          :default-active=\"activeIndex\"\n          class=\"el-menu-vertical\"\n          background-color=\"#545c64\"\n          text-color=\"#fff\"\n          active-text-color=\"#ffd04b\"\n        >\n          <el-menu-item index=\"/\">\n            <el-icon><home /></el-icon>\n            <span>首页</span>\n          </el-menu-item>\n        </el-menu>\n      </el-aside>\n      <el-container>\n        <el-header v-if=\"isLoggedIn\">\n          <div class=\"header-content\">\n            <h2>{{ pageTitle }}</h2>\n            <div class=\"user-info\">\n              <span>欢迎，{{ currentUser.username }}</span>\n              <el-button type=\"text\" @click=\"logout\">退出登录</el-button>\n            </div>\n          </div>\n        </el-header>\n        <el-main>\n          <!-- 只在根路径 / 下渲染，避免与嵌套路由冲突 -->\n          <router-view v-if=\"route.path === '/'\" />\n        </el-main>\n        <el-footer v-if=\"isLoggedIn\">\n          <p>图书管理系统 &copy; {{ new Date().getFullYear() }}</p>\n        </el-footer>\n      </el-container>\n    </el-container>\n  </div>\n</template>\n\n<script>\nimport { computed } from 'vue'\nimport { useRoute, useRouter } from 'vue-router'\nimport { ElMessage } from 'element-plus'\nimport {\n  HomeFilled as Home\n} from '@element-plus/icons-vue'\n\nexport default {\n  name: 'App',\n  components: {\n    Home\n  },\n  setup() {\n    const route = useRoute()\n    const router = useRouter()\n\n    const activeIndex = computed(() => route.path)\n\n    // 检查用户是否已登录\n    const isLoggedIn = computed(() => {\n      return localStorage.getItem('user') !== null\n    })\n\n    // 获取当前用户信息\n    const currentUser = computed(() => {\n      const userStr = localStorage.getItem('user')\n      return userStr ? JSON.parse(userStr) : { username: '' }\n    })\n\n    const pageTitle = computed(() => {\n      switch (route.path) {\n        case '/':\n          return '系统首页'\n        case '/login':\n          return '登录/注册'\n        default:\n          return '图书管理系统'\n      }\n    })\n\n    // 退出登录\n    const logout = () => {\n      localStorage.removeItem('user')\n      ElMessage.success('已退出登录')\n      router.push('/login')\n    }\n\n    return {\n      route,\n      activeIndex,\n      pageTitle,\n      isLoggedIn,\n      currentUser,\n      logout\n    }\n  }\n}\n</script>\n\n<style>\nhtml, body {\n  margin: 0;\n  padding: 0;\n  height: 100%;\n}\n\n#app {\n  font-family: Avenir, Helvetica, Arial, sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  color: #2c3e50;\n  height: 100vh;\n}\n\n.el-container {\n  height: 100%;\n}\n\n.el-aside {\n  background-color: #545c64;\n  color: white;\n}\n\n.el-header {\n  background-color: #f5f7fa;\n  border-bottom: 1px solid #e6e6e6;\n  display: flex;\n  align-items: center;\n}\n\n.el-footer {\n  background-color: #f5f7fa;\n  color: #909399;\n  text-align: center;\n  line-height: 60px;\n  border-top: 1px solid #e6e6e6;\n}\n\n.logo-container {\n  padding: 20px 0;\n  text-align: center;\n}\n\n.logo {\n  width: 60px;\n  height: 60px;\n}\n\n.header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  width: 100%;\n}\n\n.user-info {\n  display: flex;\n  align-items: center;\n}\n\n.user-info span {\n  margin-right: 10px;\n}\n\n.el-menu-vertical {\n  border-right: none;\n}\n</style>\n", "import { render } from \"./App.vue?vue&type=template&id=40ec49e7\"\nimport script from \"./App.vue?vue&type=script&lang=js\"\nexport * from \"./App.vue?vue&type=script&lang=js\"\n\nimport \"./App.vue?vue&type=style&index=0&id=40ec49e7&lang=css\"\n\nimport exportComponent from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\软件架构大作业\\\\homework\\\\node_modules\\\\vue-loader-v16\\\\dist\\\\exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render]])\n\nexport default __exports__", "import { createRouter, createWebHashHistory } from 'vue-router'\n\nconst routes = [\n  {\n    path: '/',\n    name: 'Home',\n    component: () => import('../views/Home.vue'),\n    meta: { requiresAuth: true },\n    beforeEnter: (_, __, next) => {\n      const userStr = localStorage.getItem('user')\n      const user = userStr ? JSON.parse(userStr) : null\n\n      if (user) {\n        if (user.role === 'admin') {\n          next({ path: '/admin/books' })\n        } else {\n          next({ path: '/user/books' })\n        }\n      } else {\n        next({ path: '/login' })\n      }\n    }\n  },\n  // 管理员路由\n  {\n    path: '/admin',\n    name: 'Admin',\n    component: () => import('../views/admin/AdminLayout.vue'),\n    meta: { requiresAuth: true, requiresAdmin: true },\n    children: [\n      {\n        path: 'books',\n        name: 'AdminBooks',\n        component: () => import('../views/Books.vue')\n      },\n      {\n        path: 'users',\n        name: 'AdminUsers',\n        component: () => import('../views/Users.vue')\n      },\n      {\n        path: 'borrowing',\n        name: 'AdminBorrowing',\n        component: () => import('../views/Borrowing.vue')\n      },\n      {\n        path: 'statistics',\n        name: 'AdminStatistics',\n        component: () => import('../views/Statistics.vue')\n      }\n    ]\n  },\n  // 普通用户路由\n  {\n    path: '/user',\n    name: 'UserHome',\n    component: () => import('../views/user/UserLayout.vue'),\n    meta: { requiresAuth: true },\n    children: [\n      {\n        path: 'books',\n        name: 'UserBooks',\n        component: () => import('../views/user/UserBooks.vue')\n      },\n      {\n        path: 'borrowing',\n        name: 'UserBorrowing',\n        component: () => import('../views/user/UserBorrowing.vue')\n      },\n      {\n        path: 'profile',\n        name: 'UserProfile',\n        component: () => import('../views/user/UserProfile.vue')\n      }\n    ]\n  },\n  {\n    path: '/login',\n    name: 'Login',\n    component: () => import('../views/Login.vue')\n  }\n]\n\nconst router = createRouter({\n  history: createWebHashHistory(),\n  routes\n})\n\n// 路由守卫\nrouter.beforeEach((to, _, next) => {\n  const userStr = localStorage.getItem('user')\n  const user = userStr ? JSON.parse(userStr) : null\n\n  if (to.matched.some(record => record.meta.requiresAuth)) {\n    // 需要登录的页面\n    if (!user) {\n      // 未登录，跳转到登录页\n      next({ name: 'Login' })\n    } else if (to.matched.some(record => record.meta.requiresAdmin) && user.role !== 'admin') {\n      // 需要管理员权限但用户不是管理员\n      next({ path: '/user/books' }) // 重定向到普通用户页面\n    } else {\n      // 已登录且权限符合，允许访问\n      next()\n    }\n  } else {\n    // 不需要登录的页面，直接访问\n    next()\n  }\n})\n\nexport default router\n", "// 用户认证模块\n\n// 初始状态\nconst state = {\n  user: JSON.parse(localStorage.getItem('user')) || null,\n  isAuthenticated: !!localStorage.getItem('user')\n}\n\n// getters\nconst getters = {\n  isAuthenticated: state => state.isAuthenticated,\n  currentUser: state => state.user\n}\n\n// actions\nconst actions = {\n  // 登录\n  login({ commit }, userData) {\n    return new Promise((resolve, reject) => {\n      try {\n        // 这里将来会调用后端API进行登录验证\n        // 目前模拟登录验证\n\n        // 模拟用户数据库\n        const users = [\n          { username: 'admin', password: 'admin123', role: 'admin' },\n          { username: 'user', password: 'user123', role: 'user' },\n          { username: 'test', password: 'test123', role: 'user' }\n        ];\n\n        // 查找用户并验证密码\n        const foundUser = users.find(user =>\n          user.username === userData.username &&\n          user.password === userData.password\n        );\n\n        if (!foundUser) {\n          throw new Error('用户名或密码错误');\n        }\n\n        const user = {\n          username: foundUser.username,\n          role: foundUser.role\n        }\n\n        // 保存到localStorage\n        localStorage.setItem('user', JSON.stringify(user))\n\n        // 提交mutation\n        commit('SET_USER', user)\n        commit('SET_AUTHENTICATED', true)\n\n        resolve(user)\n      } catch (error) {\n        reject(error)\n      }\n    })\n  },\n\n  // 注册\n  register(_, userInput) {\n    return new Promise((resolve, reject) => {\n      try {\n        // 这里将来会调用后端API进行注册\n        // 目前模拟注册成功\n\n        // 在实际应用中，这里应该将用户信息保存到数据库\n        // 目前我们只是模拟注册成功\n\n        // 检查用户名是否已存在\n        // 模拟用户数据库\n        const users = [\n          { username: 'admin', password: 'admin123', role: 'admin' },\n          { username: 'user', password: 'user123', role: 'user' },\n          { username: 'test', password: 'test123', role: 'user' }\n        ];\n\n        const userExists = users.some(user => user.username === userInput.username);\n\n        if (userExists) {\n          throw new Error('用户名已存在');\n        }\n\n        // 模拟注册成功\n        console.log('注册用户:', userInput.username, '密码:', userInput.password);\n        resolve({ success: true })\n      } catch (error) {\n        reject(error)\n      }\n    })\n  },\n\n  // 退出登录\n  logout({ commit }) {\n    // 清除localStorage\n    localStorage.removeItem('user')\n\n    // 提交mutation\n    commit('SET_USER', null)\n    commit('SET_AUTHENTICATED', false)\n\n    return Promise.resolve()\n  }\n}\n\n// mutations\nconst mutations = {\n  SET_USER(state, user) {\n    state.user = user\n  },\n  SET_AUTHENTICATED(state, isAuthenticated) {\n    state.isAuthenticated = isAuthenticated\n  }\n}\n\nexport default {\n  namespaced: true,\n  state,\n  getters,\n  actions,\n  mutations\n}\n", "import { createStore } from 'vuex'\nimport auth from './modules/auth'\n\n// 示例数据\nconst sampleBooks = [\n  { id: 1, title: '三体', author: '刘慈欣', category: '科幻', publisher: '重庆出版社', isbn: '9787536692930', stock: 10, borrowed: 3 },\n  { id: 2, title: '活着', author: '余华', category: '文学', publisher: '作家出版社', isbn: '9787506365437', stock: 5, borrowed: 2 },\n  { id: 3, title: '百年孤独', author: '加西亚·马尔克斯', category: '文学', publisher: '南海出版公司', isbn: '9787544253994', stock: 7, borrowed: 1 },\n  { id: 4, title: '算法导论', author: '<PERSON>', category: '计算机', publisher: '机械工业出版社', isbn: '9787111407010', stock: 3, borrowed: 0 },\n  { id: 5, title: '人类简史', author: '尤瓦尔·赫拉利', category: '历史', publisher: '中信出版社', isbn: '9787508647357', stock: 8, borrowed: 4 },\n];\n\nconst sampleUsers = [\n  { id: 1, name: '张三', contact: '13800138000', borrowedBooks: [1, 5] },\n  { id: 2, name: '李四', contact: '13900139000', borrowedBooks: [2] },\n  { id: 3, name: '王五', contact: '13700137000', borrowedBooks: [3, 1] },\n];\n\nconst sampleBorrowings = [\n  { id: 1, bookId: 1, userId: 1, borrowDate: '2023-05-01', returnDate: null },\n  { id: 2, bookId: 5, userId: 1, borrowDate: '2023-05-10', returnDate: null },\n  { id: 3, bookId: 2, userId: 2, borrowDate: '2023-05-05', returnDate: null },\n  { id: 4, bookId: 3, userId: 3, borrowDate: '2023-05-12', returnDate: null },\n  { id: 5, bookId: 1, userId: 3, borrowDate: '2023-05-15', returnDate: null },\n];\n\nexport default createStore({\n  state: {\n    books: sampleBooks,\n    users: sampleUsers,\n    borrowings: sampleBorrowings\n  },\n  getters: {\n    getBookById: (state) => (id) => {\n      return state.books.find(book => book.id === id)\n    },\n    getUserById: (state) => (id) => {\n      return state.users.find(user => user.id === id)\n    },\n    getBorrowingsByUserId: (state) => (userId) => {\n      return state.borrowings.filter(borrowing => borrowing.userId === userId)\n    },\n    getBorrowingsByBookId: (state) => (bookId) => {\n      return state.borrowings.filter(borrowing => borrowing.bookId === bookId)\n    },\n    getBooksByCategory: (state) => {\n      const categories = {};\n      state.books.forEach(book => {\n        if (!categories[book.category]) {\n          categories[book.category] = 0;\n        }\n        categories[book.category]++;\n      });\n      return categories;\n    },\n    getBorrowingStatistics: (state) => {\n      const statistics = {};\n      state.borrowings.forEach(borrowing => {\n        const book = state.books.find(b => b.id === borrowing.bookId);\n        if (book) {\n          if (!statistics[book.category]) {\n            statistics[book.category] = 0;\n          }\n          statistics[book.category]++;\n        }\n      });\n      return statistics;\n    }\n  },\n  mutations: {\n    addBook(state, book) {\n      state.books.push(book);\n    },\n    updateBook(state, updatedBook) {\n      const index = state.books.findIndex(book => book.id === updatedBook.id);\n      if (index !== -1) {\n        state.books[index] = updatedBook;\n      }\n    },\n    deleteBook(state, bookId) {\n      state.books = state.books.filter(book => book.id !== bookId);\n    },\n    addUser(state, user) {\n      state.users.push(user);\n    },\n    updateUser(state, updatedUser) {\n      const index = state.users.findIndex(user => user.id === updatedUser.id);\n      if (index !== -1) {\n        state.users[index] = updatedUser;\n      }\n    },\n    deleteUser(state, userId) {\n      state.users = state.users.filter(user => user.id !== userId);\n    },\n    addBorrowing(state, borrowing) {\n      state.borrowings.push(borrowing);\n      // 更新图书借阅数量\n      const book = state.books.find(book => book.id === borrowing.bookId);\n      if (book) {\n        book.borrowed++;\n      }\n      // 更新用户借阅记录\n      const user = state.users.find(user => user.id === borrowing.userId);\n      if (user && !user.borrowedBooks.includes(borrowing.bookId)) {\n        user.borrowedBooks.push(borrowing.bookId);\n      }\n    },\n    returnBook(state, borrowingId) {\n      const borrowing = state.borrowings.find(b => b.id === borrowingId);\n      if (borrowing) {\n        borrowing.returnDate = new Date().toISOString().split('T')[0];\n        // 更新图书借阅数量\n        const book = state.books.find(book => book.id === borrowing.bookId);\n        if (book) {\n          book.borrowed--;\n        }\n        // 更新用户借阅记录\n        const user = state.users.find(user => user.id === borrowing.userId);\n        if (user) {\n          user.borrowedBooks = user.borrowedBooks.filter(id => id !== borrowing.bookId);\n        }\n      }\n    }\n  },\n  actions: {\n    addBook({ commit }, book) {\n      commit('addBook', {\n        ...book,\n        id: Date.now(),\n        borrowed: 0\n      });\n    },\n    updateBook({ commit }, book) {\n      commit('updateBook', book);\n    },\n    deleteBook({ commit }, bookId) {\n      commit('deleteBook', bookId);\n    },\n    addUser({ commit }, user) {\n      commit('addUser', {\n        ...user,\n        id: Date.now(),\n        borrowedBooks: []\n      });\n    },\n    updateUser({ commit }, user) {\n      commit('updateUser', user);\n    },\n    deleteUser({ commit }, userId) {\n      commit('deleteUser', userId);\n    },\n    borrowBook({ commit, state }, { bookId, userId }) {\n      const book = state.books.find(b => b.id === bookId);\n      if (book && book.stock > book.borrowed) {\n        commit('addBorrowing', {\n          id: Date.now(),\n          bookId,\n          userId,\n          borrowDate: new Date().toISOString().split('T')[0],\n          returnDate: null\n        });\n        return true;\n      }\n      return false;\n    },\n    returnBook({ commit }, borrowingId) {\n      commit('returnBook', borrowingId);\n    }\n  },\n  modules: {\n    auth\n  }\n})\n", "import { createApp } from 'vue'\nimport App from './App.vue'\nimport router from './router'\nimport store from './store'\nimport ElementPlus from 'element-plus'\nimport 'element-plus/dist/index.css'\nimport * as ElementPlusIconsVue from '@element-plus/icons-vue'\n\nconst app = createApp(App)\n\n// 注册所有图标\nfor (const [key, component] of Object.entries(ElementPlusIconsVue)) {\n  app.component(key, component)\n}\n\napp.use(router)\napp.use(store)\napp.use(ElementPlus)\n\napp.mount('#app')\n", "module.exports = __webpack_public_path__ + \"img/logo.82b9c7a5.png\";", "export * from \"-!../node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader-v16/dist/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/cache-loader/dist/cjs.js??ref--1-0!../node_modules/vue-loader-v16/dist/index.js??ref--1-1!./App.vue?vue&type=style&index=0&id=40ec49e7&lang=css\""], "sourceRoot": ""}