{"version": 3, "sources": ["webpack:///./src/views/admin/AdminLayout.vue?cb3b", "webpack:///./src/views/admin/AdminLayout.vue", "webpack:///./src/views/admin/AdminLayout.vue?dc7b"], "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_container", "_component_el_aside", "width", "_createElementVNode", "alt", "src", "_imports_0", "_component_el_menu", "router", "default-active", "$setup", "activeIndex", "background-color", "text-color", "active-text-color", "_component_el_menu_item", "index", "_component_el_icon", "_component_Reading", "_component_Tickets", "_component_User", "_component_DataAnalysis", "_component_el_header", "_hoisted_2", "_toDisplayString", "pageTitle", "_hoisted_3", "currentUser", "username", "_component_el_button", "type", "onClick", "logout", "_cache", "_component_el_main", "_component_router_view", "_component_el_footer", "Date", "getFullYear", "name", "components", "Reading", "Tickets", "User", "DataAnalysis", "setup", "route", "useRoute", "useRouter", "store", "useStore", "computed", "path", "userStr", "localStorage", "getItem", "JSON", "parse", "async", "dispatch", "ElMessage", "success", "push", "__exports__", "render"], "mappings": "gHAAA,W,gHCCOA,MAAM,gB,GAoCEA,MAAM,kB,GAEJA,MAAM,a,+qBAtCrBC,gCAoDM,MApDNC,EAoDM,CAnDJC,yBAkDeC,EAAA,M,6BAjDb,IA+BW,CA/BXD,yBA+BWE,EAAA,CA/BDC,MAAM,SAAO,C,6BACrB,IAIM,C,YAJNC,gCAIM,OAJDP,MAAM,kBAAgB,CACzBO,gCAA6D,OAAxDC,IAAI,WAAWC,IAAAC,IAA4BV,MAAM,SACtDO,gCAAe,UAAX,UACJA,gCAAkC,OAA7BP,MAAM,eAAc,S,IAE3BG,yBAwBUQ,EAAA,CAvBRC,OAAA,GACCC,iBAAgBC,EAAAC,YACjBf,MAAM,mBACNgB,mBAAiB,UACjBC,aAAW,OACXC,oBAAkB,W,8BAElB,IAGe,CAHff,yBAGegB,EAAA,CAHDC,MAAM,gBAAc,C,6BAChC,IAA8B,CAA9BjB,yBAA8BkB,EAAA,M,6BAArB,IAAW,CAAXlB,yBAAWmB,K,kBACpBf,gCAAiB,YAAX,QAAI,M,aAEZJ,yBAGegB,EAAA,CAHDC,MAAM,oBAAkB,C,6BACpC,IAA8B,CAA9BjB,yBAA8BkB,EAAA,M,6BAArB,IAAW,CAAXlB,yBAAWoB,K,kBACpBhB,gCAAiB,YAAX,QAAI,M,aAEZJ,yBAGegB,EAAA,CAHDC,MAAM,gBAAc,C,6BAChC,IAA2B,CAA3BjB,yBAA2BkB,EAAA,M,6BAAlB,IAAQ,CAARlB,yBAAQqB,K,kBACjBjB,gCAAiB,YAAX,QAAI,M,aAEZJ,yBAGegB,EAAA,CAHDC,MAAM,qBAAmB,C,6BACrC,IAAmC,CAAnCjB,yBAAmCkB,EAAA,M,6BAA1B,IAAgB,CAAhBlB,yBAAgBsB,K,kBACzBlB,gCAAiB,YAAX,QAAI,M,yDAIhBJ,yBAgBeC,EAAA,M,6BAfb,IAQY,CARZD,yBAQYuB,EAAA,M,6BAPV,IAMM,CANNnB,gCAMM,MANNoB,EAMM,CALJpB,gCAAwB,UAAAqB,6BAAjBd,EAAAe,WAAS,GAChBtB,gCAGM,MAHNuB,EAGM,CAFJvB,gCAA0C,YAApC,MAAGqB,6BAAGd,EAAAiB,YAAYC,UAAQ,GAChC7B,yBAAuD8B,EAAA,CAA5CC,KAAK,OAAQC,QAAOrB,EAAAsB,Q,8BAAQ,IAAIC,EAAA,KAAAA,EAAA,I,6BAAJ,W,uCAI7ClC,yBAEUmC,EAAA,M,6BADR,IAAe,CAAfnC,yBAAeoC,K,MAEjBpC,yBAEYqC,EAAA,M,6BADV,IAAmD,CAAnDjC,gCAAmD,SAAhD,YAAcqB,8BAAA,IAAOa,MAAOC,eAAW,K,sFAmBrC,GACbC,KAAM,cACNC,WAAY,CACVC,qBACAC,qBACAC,eACAC,gCAEFC,QACE,MAAMC,EAAQC,iBACRvC,EAASwC,iBACTC,EAAQC,iBAERvC,EAAcwC,sBAAS,IAAML,EAAMM,MAGnCzB,EAAcwB,sBAAS,KAC3B,MAAME,EAAUC,aAAaC,QAAQ,QACrC,OAAOF,EAAUG,KAAKC,MAAMJ,GAAW,CAAEzB,SAAU,MAG/CH,EAAY0B,sBAAS,KACzB,OAAQL,EAAMM,MACZ,IAAK,eACH,MAAO,OACT,IAAK,mBACH,MAAO,OACT,IAAK,eACH,MAAO,OACT,IAAK,oBACH,MAAO,OACT,QACE,MAAO,YAKPpB,EAAS0B,gBACPT,EAAMU,SAAS,eACrBC,OAAUC,QAAQ,SAClBrD,EAAOsD,KAAK,WAGd,MAAO,CACLnD,cACAc,YACAE,cACAK,Y,iCC5GN,MAAM+B,EAA2B,IAAgB,EAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAE1E", "file": "js/chunk-49c316d5.d6fecff8.js", "sourcesContent": ["export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../../node_modules/vue-loader-v16/dist/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader-v16/dist/index.js??ref--1-1!./AdminLayout.vue?vue&type=style&index=0&id=2082f77e&scoped=true&lang=css\"", "<template>\n  <div class=\"admin-layout\">\n    <el-container>\n      <el-aside width=\"200px\">\n        <div class=\"logo-container\">\n          <img alt=\"Vue logo\" src=\"../../assets/logo.png\" class=\"logo\">\n          <h3>图书管理系统</h3>\n          <div class=\"admin-badge\">管理员</div>\n        </div>\n        <el-menu\n          router\n          :default-active=\"activeIndex\"\n          class=\"el-menu-vertical\"\n          background-color=\"#545c64\"\n          text-color=\"#fff\"\n          active-text-color=\"#ffd04b\"\n        >\n          <el-menu-item index=\"/admin/books\">\n            <el-icon><Reading /></el-icon>\n            <span>图书管理</span>\n          </el-menu-item>\n          <el-menu-item index=\"/admin/borrowing\">\n            <el-icon><Tickets /></el-icon>\n            <span>借阅管理</span>\n          </el-menu-item>\n          <el-menu-item index=\"/admin/users\">\n            <el-icon><User /></el-icon>\n            <span>用户管理</span>\n          </el-menu-item>\n          <el-menu-item index=\"/admin/statistics\">\n            <el-icon><DataAnalysis /></el-icon>\n            <span>数据统计</span>\n          </el-menu-item>\n        </el-menu>\n      </el-aside>\n      <el-container>\n        <el-header>\n          <div class=\"header-content\">\n            <h2>{{ pageTitle }}</h2>\n            <div class=\"user-info\">\n              <span>欢迎，{{ currentUser.username }}</span>\n              <el-button type=\"text\" @click=\"logout\">退出登录</el-button>\n            </div>\n          </div>\n        </el-header>\n        <el-main>\n          <router-view />\n        </el-main>\n        <el-footer>\n          <p>图书管理系统 &copy; {{ new Date().getFullYear() }}</p>\n        </el-footer>\n      </el-container>\n    </el-container>\n  </div>\n</template>\n\n<script>\nimport { computed } from 'vue'\nimport { useRoute, useRouter } from 'vue-router'\nimport { useStore } from 'vuex'\nimport { ElMessage } from 'element-plus'\nimport {\n  Reading,\n  Tickets,\n  User,\n  DataAnalysis\n} from '@element-plus/icons-vue'\n\nexport default {\n  name: 'AdminLayout',\n  components: {\n    Reading,\n    Tickets,\n    User,\n    DataAnalysis\n  },\n  setup() {\n    const route = useRoute()\n    const router = useRouter()\n    const store = useStore()\n\n    const activeIndex = computed(() => route.path)\n\n    // 获取当前用户信息\n    const currentUser = computed(() => {\n      const userStr = localStorage.getItem('user')\n      return userStr ? JSON.parse(userStr) : { username: '' }\n    })\n\n    const pageTitle = computed(() => {\n      switch (route.path) {\n        case '/admin/books':\n          return '图书管理'\n        case '/admin/borrowing':\n          return '借阅管理'\n        case '/admin/users':\n          return '用户管理'\n        case '/admin/statistics':\n          return '数据统计'\n        default:\n          return '图书管理系统'\n      }\n    })\n\n    // 退出登录\n    const logout = async () => {\n      await store.dispatch('auth/logout')\n      ElMessage.success('已退出登录')\n      router.push('/login')\n    }\n\n    return {\n      activeIndex,\n      pageTitle,\n      currentUser,\n      logout\n    }\n  }\n}\n</script>\n\n<style scoped>\n.admin-layout {\n  height: 100vh;\n}\n\n.el-container {\n  height: 100%;\n}\n\n.el-aside {\n  background-color: #545c64;\n  color: white;\n}\n\n.el-header {\n  background-color: #f5f7fa;\n  border-bottom: 1px solid #e6e6e6;\n  display: flex;\n  align-items: center;\n}\n\n.el-footer {\n  background-color: #f5f7fa;\n  color: #909399;\n  text-align: center;\n  line-height: 60px;\n  border-top: 1px solid #e6e6e6;\n}\n\n.logo-container {\n  padding: 20px 0;\n  text-align: center;\n  position: relative;\n}\n\n.logo {\n  width: 60px;\n  height: 60px;\n}\n\n.admin-badge {\n  position: absolute;\n  top: 10px;\n  right: 10px;\n  background-color: #f56c6c;\n  color: white;\n  padding: 2px 8px;\n  border-radius: 10px;\n  font-size: 12px;\n}\n\n.header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  width: 100%;\n}\n\n.user-info {\n  display: flex;\n  align-items: center;\n}\n\n.user-info span {\n  margin-right: 10px;\n}\n\n.el-menu-vertical {\n  border-right: none;\n}\n</style>\n", "import { render } from \"./AdminLayout.vue?vue&type=template&id=2082f77e&scoped=true\"\nimport script from \"./AdminLayout.vue?vue&type=script&lang=js\"\nexport * from \"./AdminLayout.vue?vue&type=script&lang=js\"\n\nimport \"./AdminLayout.vue?vue&type=style&index=0&id=2082f77e&scoped=true&lang=css\"\n\nimport exportComponent from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\软件架构大作业\\\\homework\\\\node_modules\\\\vue-loader-v16\\\\dist\\\\exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-2082f77e\"]])\n\nexport default __exports__"], "sourceRoot": ""}