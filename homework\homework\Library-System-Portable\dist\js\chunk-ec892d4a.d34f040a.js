(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-ec892d4a"],{"215b":function(e,t,c){"use strict";var a=c("7a23");const o={ref:"chartContainer",class:"highcharts-container"};function n(e,t,c,n,r,l){return Object(a["openBlock"])(),Object(a["createElementBlock"])("div",o,null,512)}var r={name:"HighchartsWrapper",props:{options:{type:Object,required:!0}},setup(e){const t=Object(a["ref"])(null);let c=null;const o=()=>{if(console.log("尝试创建图表...",{hasHighcharts:!!window.Highcharts,hasContainer:!!t.value,optionsType:typeof e.options,optionsKeys:e.options?Object.keys(e.options):[]}),window.Highcharts&&t.value&&e.options)try{t.value.innerHTML="",c=window.Highcharts.chart(t.value,e.options),console.log("图表创建成功:",c)}catch(a){console.error("创建Highcharts图表失败:",a),console.error("错误详情:",a.message),console.error("图表配置:",e.options),t.value&&(t.value.innerHTML=`<div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #f56c6c; flex-direction: column;">\n              <div>图表创建失败</div>\n              <div style="font-size: 12px; margin-top: 5px;">${a.message}</div>\n            </div>`)}else console.warn("Highcharts未加载或容器不存在或配置为空"),t.value&&(t.value.innerHTML='<div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #666;">正在加载图表...</div>')},n=()=>{c&&(c.destroy(),c=null)};return Object(a["onMounted"])(()=>{console.log("HighchartsWrapper mounted");const e=(c=0)=>{window.Highcharts?o():c<10?(console.log(`Highcharts未加载，重试 ${c+1}/10`),setTimeout(()=>{e(c+1)},200)):(console.error("Highcharts加载失败，请检查CDN连接"),t.value&&(t.value.innerHTML='<div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #f56c6c;">图表加载失败，请检查网络连接</div>'))};e()}),Object(a["onUnmounted"])(()=>{n()}),Object(a["watch"])(()=>e.options,()=>{n(),o()},{deep:!0}),{chartContainer:t}}},l=(c("bb66"),c("6b0d")),s=c.n(l);const i=s()(r,[["render",n],["__scopeId","data-v-f326fe4a"]]);t["a"]=i},"4f15":function(e,t,c){},bb51:function(e,t,c){"use strict";c.r(t);var a=c("7a23");const o={class:"home"},n={class:"card-content"},r={class:"card-content"},l={class:"card-content"},s={class:"card-content"},i={class:"card-header"},d={class:"chart-container"},b={class:"card-header"},O={class:"chart-container"},j={class:"card-header"},u={class:"features-showcase"},h={class:"feature-item"},p={class:"feature-item"},m={class:"feature-item"},g={class:"feature-item"};function f(e,t,c,f,N,V){const x=Object(a["resolveComponent"])("el-col"),v=Object(a["resolveComponent"])("el-row"),w=Object(a["resolveComponent"])("el-card"),C=Object(a["resolveComponent"])("el-tag"),y=Object(a["resolveComponent"])("HighchartsWrapper"),E=Object(a["resolveComponent"])("el-button"),_=Object(a["resolveComponent"])("TrendCharts"),H=Object(a["resolveComponent"])("el-icon"),k=Object(a["resolveComponent"])("Download"),D=Object(a["resolveComponent"])("Monitor"),z=Object(a["resolveComponent"])("Connection");return Object(a["openBlock"])(),Object(a["createElementBlock"])("div",o,[Object(a["createVNode"])(v,{gutter:20},{default:Object(a["withCtx"])(()=>[Object(a["createVNode"])(x,{span:24},{default:Object(a["withCtx"])(()=>t[0]||(t[0]=[Object(a["createElementVNode"])("h1",null,"图书管理系统",-1),Object(a["createElementVNode"])("p",null,"欢迎使用图书管理系统，您可以通过导航菜单访问各个功能模块。",-1)])),_:1,__:[0]})]),_:1}),Object(a["createVNode"])(v,{gutter:20,class:"dashboard"},{default:Object(a["withCtx"])(()=>[Object(a["createVNode"])(x,{span:6},{default:Object(a["withCtx"])(()=>[Object(a["createVNode"])(w,{class:"box-card"},{header:Object(a["withCtx"])(()=>t[1]||(t[1]=[Object(a["createElementVNode"])("div",{class:"card-header"},[Object(a["createElementVNode"])("span",null,"图书总数")],-1)])),default:Object(a["withCtx"])(()=>[Object(a["createElementVNode"])("div",n,[Object(a["createElementVNode"])("h2",null,Object(a["toDisplayString"])(f.books.length),1)])]),_:1})]),_:1}),Object(a["createVNode"])(x,{span:6},{default:Object(a["withCtx"])(()=>[Object(a["createVNode"])(w,{class:"box-card"},{header:Object(a["withCtx"])(()=>t[2]||(t[2]=[Object(a["createElementVNode"])("div",{class:"card-header"},[Object(a["createElementVNode"])("span",null,"用户总数")],-1)])),default:Object(a["withCtx"])(()=>[Object(a["createElementVNode"])("div",r,[Object(a["createElementVNode"])("h2",null,Object(a["toDisplayString"])(f.users.length),1)])]),_:1})]),_:1}),Object(a["createVNode"])(x,{span:6},{default:Object(a["withCtx"])(()=>[Object(a["createVNode"])(w,{class:"box-card"},{header:Object(a["withCtx"])(()=>t[3]||(t[3]=[Object(a["createElementVNode"])("div",{class:"card-header"},[Object(a["createElementVNode"])("span",null,"借阅总数")],-1)])),default:Object(a["withCtx"])(()=>[Object(a["createElementVNode"])("div",l,[Object(a["createElementVNode"])("h2",null,Object(a["toDisplayString"])(f.borrowings.length),1)])]),_:1})]),_:1}),Object(a["createVNode"])(x,{span:6},{default:Object(a["withCtx"])(()=>[Object(a["createVNode"])(w,{class:"box-card"},{header:Object(a["withCtx"])(()=>t[4]||(t[4]=[Object(a["createElementVNode"])("div",{class:"card-header"},[Object(a["createElementVNode"])("span",null,"当前借出")],-1)])),default:Object(a["withCtx"])(()=>[Object(a["createElementVNode"])("div",s,[Object(a["createElementVNode"])("h2",null,Object(a["toDisplayString"])(f.activeBorrowings),1)])]),_:1})]),_:1})]),_:1}),Object(a["createVNode"])(v,{gutter:20,class:"charts-row"},{default:Object(a["withCtx"])(()=>[Object(a["createVNode"])(x,{span:12},{default:Object(a["withCtx"])(()=>[Object(a["createVNode"])(w,{class:"box-card"},{header:Object(a["withCtx"])(()=>[Object(a["createElementVNode"])("div",i,[t[6]||(t[6]=Object(a["createElementVNode"])("span",null,"图书分类统计 - Highcharts",-1)),Object(a["createVNode"])(C,{size:"small",type:"success"},{default:Object(a["withCtx"])(()=>t[5]||(t[5]=[Object(a["createTextVNode"])("实时数据")])),_:1,__:[5]})])]),default:Object(a["withCtx"])(()=>[Object(a["createElementVNode"])("div",d,[Object(a["createVNode"])(y,{options:f.categoryChartOptions,class:"chart"},null,8,["options"])])]),_:1})]),_:1}),Object(a["createVNode"])(x,{span:12},{default:Object(a["withCtx"])(()=>[Object(a["createVNode"])(w,{class:"box-card"},{header:Object(a["withCtx"])(()=>[Object(a["createElementVNode"])("div",b,[t[8]||(t[8]=Object(a["createElementVNode"])("span",null,"系统状态仪表盘 - Highcharts",-1)),Object(a["createVNode"])(C,{size:"small",type:"warning"},{default:Object(a["withCtx"])(()=>t[7]||(t[7]=[Object(a["createTextVNode"])("动态更新")])),_:1,__:[7]})])]),default:Object(a["withCtx"])(()=>[Object(a["createElementVNode"])("div",O,[Object(a["createVNode"])(y,{options:f.gaugeChartOptions,class:"chart"},null,8,["options"])])]),_:1})]),_:1})]),_:1}),Object(a["createVNode"])(v,{gutter:20,class:"charts-row"},{default:Object(a["withCtx"])(()=>[Object(a["createVNode"])(x,{span:24},{default:Object(a["withCtx"])(()=>[Object(a["createVNode"])(w,{class:"box-card"},{header:Object(a["withCtx"])(()=>[Object(a["createElementVNode"])("div",j,[t[10]||(t[10]=Object(a["createElementVNode"])("span",null,"Highcharts功能特色展示",-1)),Object(a["createVNode"])(E,{size:"small",type:"primary",onClick:f.goToHighchartsDemo},{default:Object(a["withCtx"])(()=>t[9]||(t[9]=[Object(a["createTextVNode"])(" 查看完整演示 ")])),_:1,__:[9]},8,["onClick"])])]),default:Object(a["withCtx"])(()=>[Object(a["createElementVNode"])("div",u,[Object(a["createVNode"])(v,{gutter:20},{default:Object(a["withCtx"])(()=>[Object(a["createVNode"])(x,{span:6},{default:Object(a["withCtx"])(()=>[Object(a["createElementVNode"])("div",h,[Object(a["createVNode"])(H,{class:"feature-icon"},{default:Object(a["withCtx"])(()=>[Object(a["createVNode"])(_)]),_:1}),t[11]||(t[11]=Object(a["createElementVNode"])("h4",null,"多种图表类型",-1)),t[12]||(t[12]=Object(a["createElementVNode"])("p",null,"支持柱状图、饼图、折线图、面积图、仪表盘、热力图、3D图表等",-1))])]),_:1}),Object(a["createVNode"])(x,{span:6},{default:Object(a["withCtx"])(()=>[Object(a["createElementVNode"])("div",p,[Object(a["createVNode"])(H,{class:"feature-icon"},{default:Object(a["withCtx"])(()=>[Object(a["createVNode"])(k)]),_:1}),t[13]||(t[13]=Object(a["createElementVNode"])("h4",null,"导出功能",-1)),t[14]||(t[14]=Object(a["createElementVNode"])("p",null,"支持导出PNG、JPEG、PDF、SVG格式，以及打印功能",-1))])]),_:1}),Object(a["createVNode"])(x,{span:6},{default:Object(a["withCtx"])(()=>[Object(a["createElementVNode"])("div",m,[Object(a["createVNode"])(H,{class:"feature-icon"},{default:Object(a["withCtx"])(()=>[Object(a["createVNode"])(D)]),_:1}),t[15]||(t[15]=Object(a["createElementVNode"])("h4",null,"响应式设计",-1)),t[16]||(t[16]=Object(a["createElementVNode"])("p",null,"自适应各种屏幕尺寸，支持移动设备和触摸操作",-1))])]),_:1}),Object(a["createVNode"])(x,{span:6},{default:Object(a["withCtx"])(()=>[Object(a["createElementVNode"])("div",g,[Object(a["createVNode"])(H,{class:"feature-icon"},{default:Object(a["withCtx"])(()=>[Object(a["createVNode"])(z)]),_:1}),t[17]||(t[17]=Object(a["createElementVNode"])("h4",null,"交互功能",-1)),t[18]||(t[18]=Object(a["createElementVNode"])("p",null,"丰富的鼠标交互、数据钻取、缩放平移等功能",-1))])]),_:1})]),_:1})])]),_:1})]),_:1})]),_:1})])}c("14d9"),c("e9f5"),c("910d");var N=c("5502"),V=c("6605"),x=c("215b"),v={name:"HomeView",components:{HighchartsWrapper:x["a"]},setup(){const e=Object(N["b"])(),t=Object(V["d"])(),c=Object(a["computed"])(()=>e.state.books),o=Object(a["computed"])(()=>e.state.users),n=Object(a["computed"])(()=>e.state.borrowings),r=Object(a["computed"])(()=>n.value.filter(e=>null===e.returnDate).length),l=Object(a["computed"])(()=>e.getters.getBooksByCategory),s=Object(a["computed"])(()=>e.getters.getBorrowingStatistics),i=()=>({credits:{enabled:!1},exporting:{enabled:!1},responsive:{rules:[{condition:{maxWidth:500},chartOptions:{legend:{layout:"horizontal",align:"center",verticalAlign:"bottom"}}}]}}),d=Object(a["computed"])(()=>({...i(),chart:{type:"column",height:250},title:{text:"图书分类分布",style:{fontSize:"14px"}},xAxis:{categories:Object.keys(l.value),labels:{style:{fontSize:"12px"}}},yAxis:{title:{text:"数量",style:{fontSize:"12px"}},min:0,labels:{style:{fontSize:"12px"}}},series:[{name:"图书数量",data:Object.values(l.value),colorByPoint:!0,colors:["#5470c6","#91cc75","#fac858","#ee6666","#73c0de","#3ba272"]}],plotOptions:{column:{dataLabels:{enabled:!0,style:{fontSize:"11px"}},borderRadius:2}},legend:{enabled:!1},tooltip:{pointFormat:"<b>{point.y}</b> 本图书"}})),b=Object(a["computed"])(()=>{const e=Math.round(r.value/c.value.length*100);return{...i(),chart:{type:"solidgauge",height:250},title:{text:"系统使用率",style:{fontSize:"14px"}},pane:{center:["50%","75%"],size:"100%",startAngle:-90,endAngle:90,background:{backgroundColor:"#EEE",innerRadius:"60%",outerRadius:"100%",shape:"arc"}},yAxis:{min:0,max:100,stops:[[.1,"#55BF3B"],[.5,"#DDDF0D"],[.9,"#DF5353"]],lineWidth:0,tickWidth:0,minorTickInterval:null,tickAmount:2,title:{y:-50,text:"使用率 (%)",style:{fontSize:"12px"}},labels:{y:16,style:{fontSize:"12px"}}},series:[{name:"使用率",data:[e],dataLabels:{format:'<div style="text-align:center"><span style="font-size:20px">{y}</span><br/><span style="font-size:10px;opacity:0.4">%</span></div>'}}],plotOptions:{solidgauge:{dataLabels:{y:5,borderWidth:0,useHTML:!0}}}}}),O=()=>{t.push("/admin/highcharts")};return{books:c,users:o,borrowings:n,activeBorrowings:r,categoryData:l,borrowingData:s,categoryChartOptions:d,gaugeChartOptions:b,goToHighchartsDemo:O}}},w=(c("dd94"),c("6b0d")),C=c.n(w);const y=C()(v,[["render",f],["__scopeId","data-v-382f89fb"]]);t["default"]=y},bb66:function(e,t,c){"use strict";c("df30")},dd94:function(e,t,c){"use strict";c("4f15")},df30:function(e,t,c){}}]);
//# sourceMappingURL=chunk-ec892d4a.d34f040a.js.map