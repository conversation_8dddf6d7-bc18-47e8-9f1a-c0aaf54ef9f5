<template>
  <div class="chat-container">
    <!-- 动态背景粒子 -->
    <div class="particles-background">
      <div class="particle" v-for="n in 20" :key="n" :style="getParticleStyle(n)"></div>
    </div>
    
    <div class="chat-wrapper">
      <!-- 头部 -->
      <div class="chat-header">
        <div class="header-content">
          <div class="ai-avatar">
            <div class="avatar-icon">🤖</div>
            <div class="status-indicator"></div>
            <div class="avatar-glow"></div>
          </div>
          <div class="header-info">
            <h3>AI 智能助手</h3>
            <p class="status-text">
              <span class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
              </span>
              在线 · 随时为您服务
            </p>
          </div>
        </div>
        <el-button text @click="clearChat" class="clear-btn">
          <el-icon><Delete /></el-icon>
        </el-button>
      </div>

      <!-- 消息区域 -->
      <div class="chat-messages" ref="messagesContainer">
        <div class="welcome-message" v-if="messages.length === 0">
          <div class="welcome-animation">
            <div class="welcome-icon">✨</div>
            <div class="floating-elements">
              <div class="float-element">📚</div>
              <div class="float-element">🔍</div>
              <div class="float-element">💡</div>
            </div>
          </div>
          <h4>欢迎使用AI助手</h4>
          <p>我是您的智能助手，可以回答各种问题。请随时向我提问！</p>
          <div class="quick-actions">
            <div class="quick-action" @click="sendQuickMessage('搜索图书')">
              <span class="action-icon">📖</span>
              <span>搜索图书</span>
            </div>
            <div class="quick-action" @click="sendQuickMessage('我的借阅记录')">
              <span class="action-icon">📋</span>
              <span>借阅记录</span>
            </div>
            <div class="quick-action" @click="sendQuickMessage('个人信息')">
              <span class="action-icon">👤</span>
              <span>个人信息</span>
            </div>
          </div>
        </div>
        
        <div v-for="(message, index) in messages" :key="index" 
             :class="['message', message.role === 'user' ? 'user-message' : 'assistant-message']"
             :style="{ animationDelay: `${index * 0.1}s` }">
          <div class="message-content">
            <div class="message-avatar" v-if="message.role === 'assistant'">
              <div class="ai-avatar-small">🤖</div>
              <div class="avatar-ripple"></div>
            </div>
            <div class="message-bubble">
              <div class="message-text" v-if="message.role === 'user'">
                {{ message.content }}
                <div class="message-shine"></div>
              </div>
              <div class="message-text" v-else>
                <div v-html="formatMessage(message.content)"></div>
                <div class="message-shine"></div>
              </div>
              <div class="message-time">
                {{ formatTime(message.timestamp) }}
              </div>
            </div>
            <div class="message-avatar" v-if="message.role === 'user'">
              <div class="user-avatar-small">👤</div>
              <div class="avatar-ripple"></div>
            </div>
          </div>
        </div>
        
        <!-- 流式输出消息 -->
        <div v-if="streamingMessage" class="message assistant-message streaming">
          <div class="message-content">
            <div class="message-avatar">
              <div class="ai-avatar-small">🤖</div>
              <div class="avatar-ripple active"></div>
            </div>
            <div class="message-bubble">
              <div class="message-text">
                <div v-html="formatMessage(streamingMessage)"></div>
                <span class="typing-cursor"></span>
                <div class="message-shine active"></div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 工具调用状态显示 -->
        <div v-if="toolCallStatus" class="message assistant-message tool-calling">
          <div class="message-content">
            <div class="message-avatar">
              <div class="ai-avatar-small">🤖</div>
              <div class="avatar-ripple active"></div>
            </div>
            <div class="message-bubble">
              <div class="tool-call-status">
                <div class="tool-status-header">
                  <span class="tool-status-message">{{ toolCallStatus.message }}</span>
                  <div class="status-wave"></div>
                </div>
                <div class="tool-list" v-if="toolCallStatus.tools && toolCallStatus.tools.length > 0">
                  <div 
                    v-for="tool in toolCallStatus.tools" 
                    :key="tool.name"
                    class="tool-item"
                    :class="tool.status"
                  >
                    <div class="tool-icon">
                      <span v-if="tool.status === 'pending'">⏳</span>
                      <span v-else-if="tool.status === 'executing'" class="executing-icon">🔄</span>
                      <span v-else-if="tool.status === 'success'">✅</span>
                      <span v-else-if="tool.status === 'error'">❌</span>
                    </div>
                    <div class="tool-info">
                      <span class="tool-name">{{ getToolDisplayName(tool.name) }}</span>
                      <span v-if="tool.error" class="tool-error">{{ tool.error }}</span>
                    </div>
                    <div class="tool-progress" v-if="tool.status === 'executing'"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 加载状态 -->
        <div v-if="loading && !streamingMessage && !toolCallStatus" class="message assistant-message loading">
          <div class="message-content">
            <div class="message-avatar">
              <div class="ai-avatar-small">🤖</div>
              <div class="avatar-ripple active"></div>
            </div>
            <div class="message-bubble">
              <div class="typing-indicator">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 输入区域 -->
      <div class="chat-input">
        <div class="input-wrapper">
          <div class="input-container">
            <el-input
              v-model="userInput"
              type="textarea"
              :rows="1"
              :autosize="{ minRows: 1, maxRows: 4 }"
              placeholder="输入消息... (Ctrl+Enter发送)"
              @keyup.enter.ctrl="sendMessage"
              @keyup.enter.exact.prevent="sendMessage"
              @focus="onInputFocus"
              @blur="onInputBlur"
              class="message-input"
            />
            <div class="input-glow" :class="{ active: inputFocused }"></div>
          </div>
          <el-button 
            type="primary" 
            @click="sendMessage" 
            :loading="loading"
            :disabled="!userInput.trim()"
            class="send-btn"
            circle
          >
            <el-icon><Position /></el-icon>
            <div class="btn-ripple"></div>
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Delete, Position } from '@element-plus/icons-vue'
import api from '@/api'

export default {
  name: 'UserChatBot',
  components: {
    Delete,
    Position
  },
  setup() {
    const messages = ref([])
    const userInput = ref('')
    const loading = ref(false)
    const messagesContainer = ref(null)
    const streamingMessage = ref('')
    const toolCallStatus = ref(null) // 工具调用状态
    const inputFocused = ref(false)

    // 定义可用的工具
    const tools = [
      {
        type: "function",
        function: {
          name: "search_books",
          description: "搜索图书信息，可以按书名、作者或分类搜索",
          parameters: {
            type: "object",
            properties: {
              query: {
                type: "string",
                description: "搜索关键词"
              },
              type: {
                type: "string",
                enum: ["title", "author", "category", "all"],
                description: "搜索类型：title(书名)、author(作者)、category(分类)、all(全部)"
              }
            },
            required: ["query"]
          }
        }
      },
      {
        type: "function",
        function: {
          name: "get_my_borrowings",
          description: "获取当前用户的借阅记录",
          parameters: {
            type: "object",
            properties: {},
            required: []
          }
        }
      },
      {
        type: "function",
        function: {
          name: "borrow_book",
          description: "借阅指定的图书",
          parameters: {
            type: "object",
            properties: {
              bookId: {
                type: "string",
                description: "要借阅的图书ID"
              }
            },
            required: ["bookId"]
          }
        }
      },
      {
        type: "function",
        function: {
          name: "return_book",
          description: "归还指定的图书",
          parameters: {
            type: "object",
            properties: {
              bookId: {
                type: "string",
                description: "要归还的图书ID"
              }
            },
            required: ["bookId"]
          }
        }
      },
      {
        type: "function",
        function: {
          name: "get_user_info",
          description: "获取当前用户的个人信息",
          parameters: {
            type: "object",
            properties: {},
            required: []
          }
        }
      },
      {
        type: "function",
        function: {
          name: "get_book_details",
          description: "获取指定图书的详细信息",
          parameters: {
            type: "object",
            properties: {
              bookId: {
                type: "string",
                description: "图书ID"
              }
            },
            required: ["bookId"]
          }
        }
      }
    ]

    // 工具调用函数
    const toolFunctions = {
      search_books: async (params) => {
        try {
          const { query, type = "all" } = params
          let result

          if (type === "title") {
            result = await api.books.searchByTitle(query)
          } else if (type === "author") {
            result = await api.books.searchByAuthor(query)
          } else if (type === "category") {
            result = await api.books.searchByCategory(query)
          } else {
            // 搜索所有图书然后过滤
            result = await api.books.getAll()
            if (result.success && result.data) {
              const filteredBooks = result.data.filter(book => 
                book.title.toLowerCase().includes(query.toLowerCase()) ||
                book.author.toLowerCase().includes(query.toLowerCase()) ||
                book.category.toLowerCase().includes(query.toLowerCase())
              )
              result.data = filteredBooks
            }
          }

          if (result.success) {
            const books = result.data || []
            if (books.length === 0) {
              return `没有找到与"${query}"相关的图书。`
            }
            
            let response = `找到 ${books.length} 本相关图书：\n\n`
            books.slice(0, 5).forEach((book, index) => {
              response += `${index + 1}. **${book.title}**\n`
              response += `   作者：${book.author}\n`
              response += `   分类：${book.category}\n`
              response += `   出版社：${book.publisher}\n`
              response += `   库存：${book.stock - book.borrowed}/${book.stock}\n`
              response += `   状态：${book.stock > book.borrowed ? '✅ 可借阅' : '❌ 已借完'}\n\n`
            })
            
            if (books.length > 5) {
              response += `还有 ${books.length - 5} 本图书，请使用更具体的关键词搜索。`
            }
            
            return response
          } else {
            return `搜索图书时出错：${result.error}`
          }
        } catch (error) {
          return `搜索图书时出错：${error.message}`
        }
      },

      get_my_borrowings: async () => {
        try {
          const result = await api.userBooks.getMyBooks()
          if (result.success) {
            const borrowings = result.data || []
            if (borrowings.length === 0) {
              return "您当前没有借阅任何图书。"
            }
            
            let response = `您当前借阅了 ${borrowings.length} 本图书：\n\n`
            borrowings.forEach((borrowing, index) => {
              response += `${index + 1}. **${borrowing.book.title}**\n`
              response += `   作者：${borrowing.book.author}\n`
              response += `   借阅时间：${new Date(borrowing.borrowDate).toLocaleDateString()}\n`
              response += `   应还时间：${new Date(borrowing.dueDate).toLocaleDateString()}\n\n`
            })
            
            return response
          } else {
            return `获取借阅记录时出错：${result.error}`
          }
        } catch (error) {
          return `获取借阅记录时出错：${error.message}`
        }
      },

      borrow_book: async (params) => {
        try {
          const { bookId } = params
          const result = await api.userBooks.borrowBook(bookId)
          if (result.success) {
            return `✅ 图书借阅成功！请按时归还。`
          } else {
            return `❌ 借阅失败：${result.error}`
          }
        } catch (error) {
          return `❌ 借阅失败：${error.message}`
        }
      },

      return_book: async (params) => {
        try {
          const { bookId } = params
          const result = await api.userBooks.returnBook(bookId)
          if (result.success) {
            return `✅ 图书归还成功！感谢您按时归还。`
          } else {
            return `❌ 归还失败：${result.error}`
          }
        } catch (error) {
          return `❌ 归还失败：${error.message}`
        }
      },

      get_user_info: async () => {
        try {
          const result = await api.users.getMe()
          if (result.success) {
            const user = result.data
            let response = `**个人信息**\n\n`
            response += `用户名：${user.username}\n`
            response += `邮箱：${user.email}\n`
            response += `角色：${user.role === 'ROLE_ADMIN' ? '管理员' : '普通用户'}\n`
            response += `注册时间：${new Date(user.createdAt).toLocaleDateString()}\n`
            
            return response
          } else {
            return `获取用户信息时出错：${result.error}`
          }
        } catch (error) {
          return `获取用户信息时出错：${error.message}`
        }
      },

      get_book_details: async (params) => {
        try {
          const { bookId } = params
          const result = await api.books.getById(bookId)
          if (result.success) {
            const book = result.data
            let response = `**图书详情**\n\n`
            response += `书名：${book.title}\n`
            response += `作者：${book.author}\n`
            response += `分类：${book.category}\n`
            response += `出版社：${book.publisher}\n`
            response += `ISBN：${book.isbn}\n`
            response += `总库存：${book.stock}\n`
            response += `已借出：${book.borrowed}\n`
            response += `可借数量：${book.stock - book.borrowed}\n`
            response += `状态：${book.stock > book.borrowed ? '✅ 可借阅' : '❌ 已借完'}\n`
            
            return response
          } else {
            return `获取图书详情时出错：${result.error}`
          }
        } catch (error) {
          return `获取图书详情时出错：${error.message}`
        }
      }
    }

    // 格式化消息内容（支持简单的换行）
    const formatMessage = (content) => {
      return content.replace(/\n/g, '<br>')
    }

    // 格式化时间
    const formatTime = (timestamp) => {
      if (!timestamp) return ''
      const date = new Date(timestamp)
      return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
    }

    // 滚动到底部
    const scrollToBottom = async () => {
      await nextTick()
      if (messagesContainer.value) {
        messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
      }
    }

    // 处理工具调用
    const handleToolCalls = async (toolCalls) => {
      const toolResults = []
      
      // 显示工具调用开始提示
      toolCallStatus.value = {
        type: 'calling',
        message: '🔧 正在调用工具...',
        tools: toolCalls.map(call => ({
          name: call.function.name,
          status: 'pending'
        }))
      }
      await scrollToBottom()
      
      for (const toolCall of toolCalls) {
        const { id, function: func } = toolCall
        const { name, arguments: args } = func
        
        // 更新当前工具状态
        toolCallStatus.value.tools = toolCallStatus.value.tools.map(tool => 
          tool.name === name ? { ...tool, status: 'executing' } : tool
        )
        toolCallStatus.value.message = `🔧 正在执行: ${getToolDisplayName(name)}`
        await scrollToBottom()
        
        try {
          const params = JSON.parse(args)
          const toolFunction = toolFunctions[name]
          
          if (toolFunction) {
            const result = await toolFunction(params)
            toolResults.push({
              tool_call_id: id,
              output: result
            })
            
            // 更新工具执行成功状态
            toolCallStatus.value.tools = toolCallStatus.value.tools.map(tool => 
              tool.name === name ? { ...tool, status: 'success' } : tool
            )
          } else {
            const errorMsg = `未知的工具函数: ${name}`
            toolResults.push({
              tool_call_id: id,
              output: errorMsg
            })
            
            // 更新工具执行失败状态
            toolCallStatus.value.tools = toolCallStatus.value.tools.map(tool => 
              tool.name === name ? { ...tool, status: 'error', error: errorMsg } : tool
            )
          }
        } catch (error) {
          const errorMsg = `工具调用出错: ${error.message}`
          toolResults.push({
            tool_call_id: id,
            output: errorMsg
          })
          
          // 更新工具执行失败状态
          toolCallStatus.value.tools = toolCallStatus.value.tools.map(tool => 
            tool.name === name ? { ...tool, status: 'error', error: errorMsg } : tool
          )
        }
        
        await scrollToBottom()
      }
      
      // 显示工具调用完成提示
      toolCallStatus.value.message = '✅ 工具调用完成，正在生成回复...'
      await scrollToBottom()
      
      return toolResults
    }

    // 获取工具显示名称
    const getToolDisplayName = (toolName) => {
      const displayNames = {
        'search_books': '搜索图书',
        'get_my_borrowings': '获取借阅记录',
        'borrow_book': '借阅图书',
        'return_book': '归还图书',
        'get_user_info': '获取用户信息',
        'get_book_details': '获取图书详情'
      }
      return displayNames[toolName] || toolName
    }

    // 流式输出处理
    const handleStreamResponse = async (response) => {
      const reader = response.body.getReader()
      const decoder = new TextDecoder()
      streamingMessage.value = ''
      let toolCalls = []
      
      try {
        for(;;) {
          const { done, value } = await reader.read()
          if (done) break
          
          const chunk = decoder.decode(value)
          const lines = chunk.split('\n')
          
          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(6)
              if (data === '[DONE]') {
                // 如果有工具调用，处理它们
                if (toolCalls.length > 0) {
                  const toolResults = await handleToolCalls(toolCalls)
                  
                  // 发送工具调用结果给AI
                  const toolMessage = {
                    role: 'assistant',
                    content: null,
                    tool_calls: toolCalls
                  }
                  
                  // 继续对话，包含工具调用结果
                  await continueConversationWithTools(toolMessage, toolResults)
                  return
                }
                
                // 普通消息结束，添加到历史记录
                if (streamingMessage.value) {
                  messages.value.push({
                    role: 'assistant',
                    content: streamingMessage.value,
                    timestamp: Date.now()
                  })
                }
                streamingMessage.value = ''
                toolCallStatus.value = null // 清除工具调用状态
                return
              }
              
              try {
                const parsed = JSON.parse(data)
                if (parsed.choices && parsed.choices[0] && parsed.choices[0].delta) {
                  const delta = parsed.choices[0].delta
                  
                  // 处理工具调用
                  if (delta.tool_calls) {
                    for (const toolCall of delta.tool_calls) {
                      if (toolCall.index !== undefined) {
                        if (!toolCalls[toolCall.index]) {
                          toolCalls[toolCall.index] = {
                            id: toolCall.id || '',
                            type: toolCall.type || 'function',
                            function: {
                              name: toolCall.function?.name || '',
                              arguments: toolCall.function?.arguments || ''
                            }
                          }
                        } else {
                          if (toolCall.function?.name) {
                            toolCalls[toolCall.index].function.name += toolCall.function.name
                          }
                          if (toolCall.function?.arguments) {
                            toolCalls[toolCall.index].function.arguments += toolCall.function.arguments
                          }
                        }
                      }
                    }
                  }
                  
                  // 处理普通内容
                  if (delta.content) {
                    streamingMessage.value += delta.content
                    await scrollToBottom()
                  }
                }
              } catch (e) {
                // 忽略解析错误
              }
            }
          }
        }
      } catch (error) {
        console.error('Stream reading error:', error)
        if (streamingMessage.value) {
          messages.value.push({
            role: 'assistant',
            content: streamingMessage.value,
            timestamp: Date.now()
          })
          streamingMessage.value = ''
        }
        toolCallStatus.value = null // 清除工具调用状态
      }
    }

    // 继续对话（包含工具调用结果）
    const continueConversationWithTools = async (toolMessage, toolResults) => {
      try {
        // 构建包含工具调用结果的消息历史
        const conversationHistory = [
          ...messages.value.map(msg => ({
            role: msg.role,
            content: msg.content
          })),
          toolMessage,
          ...toolResults.map(result => ({
            role: 'tool',
            tool_call_id: result.tool_call_id,
            content: result.output
          }))
        ]

        const response = await fetch('https://api.deepseek.com/chat/completions', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer sk-e37cf8802da84d6982dd702f3b883d08`
          },
          body: JSON.stringify({
            model: 'deepseek-chat',
            messages: conversationHistory,
            temperature: 0.7,
            stream: true,
            tools: tools
          })
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        await handleStreamResponse(response)
      } catch (error) {
        console.error('Continue conversation error:', error)
        ElMessage.error('处理工具调用结果时出错：' + error.message)
        loading.value = false
        toolCallStatus.value = null // 清除工具调用状态
      }
    }

    // 发送消息
    const sendMessage = async () => {
      if (!userInput.value.trim()) {
        ElMessage.warning('请输入消息内容')
        return
      }

      const userMessage = userInput.value.trim()
      messages.value.push({
        role: 'user',
        content: userMessage,
        timestamp: Date.now()
      })
      userInput.value = ''
      await scrollToBottom()

      loading.value = true
      try {
        const response = await fetch('https://api.deepseek.com/chat/completions', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer sk-e37cf8802da84d6982dd702f3b883d08`
          },
          body: JSON.stringify({
            model: 'deepseek-chat',
            messages: [
              {
                role: 'system',
                content: `你是一个图书管理系统的AI助手。你可以帮助用户：
1. 搜索图书（按书名、作者或分类）
2. 查看个人借阅记录
3. 借阅和归还图书
4. 查看个人信息
5. 获取图书详细信息

请用友好、专业的语气回答用户问题。当用户询问图书相关信息时，主动使用工具来获取准确的数据。`
              },
              ...messages.value.map(msg => ({
                role: msg.role,
                content: msg.content
              }))
            ],
            temperature: 0.7,
            stream: true,
            tools: tools
          })
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        loading.value = false
        await handleStreamResponse(response)
      } catch (error) {
        console.error('Send message error:', error)
        ElMessage.error('发送消息失败：' + error.message)
        loading.value = false
      } finally {
        await scrollToBottom()
      }
    }

    // 发送快速消息
    const sendQuickMessage = async (message) => {
      const userMessage = message.trim()
      messages.value.push({
        role: 'user',
        content: userMessage,
        timestamp: Date.now()
      })
      userInput.value = ''
      await scrollToBottom()

      loading.value = true
      try {
        const response = await fetch('https://api.deepseek.com/chat/completions', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer sk-e37cf8802da84d6982dd702f3b883d08`
          },
          body: JSON.stringify({
            model: 'deepseek-chat',
            messages: [
              {
                role: 'system',
                content: `你是一个图书管理系统的AI助手。你可以帮助用户：
1. 搜索图书（按书名、作者或分类）
2. 查看个人借阅记录
3. 借阅和归还图书
4. 查看个人信息
5. 获取图书详细信息

请用友好、专业的语气回答用户问题。当用户询问图书相关信息时，主动使用工具来获取准确的数据。`
              },
              ...messages.value.map(msg => ({
                role: msg.role,
                content: msg.content
              }))
            ],
            temperature: 0.7,
            stream: true,
            tools: tools
          })
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        loading.value = false
        await handleStreamResponse(response)
      } catch (error) {
        console.error('Send quick message error:', error)
        ElMessage.error('发送快速消息失败：' + error.message)
        loading.value = false
      } finally {
        await scrollToBottom()
      }
    }

    // 输入框聚焦
    const onInputFocus = () => {
      inputFocused.value = true
    }

    // 输入框失焦
    const onInputBlur = () => {
      inputFocused.value = false
    }

    // 清空对话
    const clearChat = () => {
      messages.value = []
      streamingMessage.value = ''
      toolCallStatus.value = null
    }

    // 生成粒子样式
    const getParticleStyle = () => {
      const size = Math.random() * 4 + 2
      const duration = Math.random() * 20 + 10
      const delay = Math.random() * 5
      const x = Math.random() * 100
      const y = Math.random() * 100
      
      return {
        width: `${size}px`,
        height: `${size}px`,
        left: `${x}%`,
        top: `${y}%`,
        animationDuration: `${duration}s`,
        animationDelay: `${delay}s`
      }
    }

    onMounted(() => {
      scrollToBottom()
    })

    return {
      messages,
      userInput,
      loading,
      messagesContainer,
      streamingMessage,
      toolCallStatus,
      inputFocused,
      formatMessage,
      formatTime,
      getToolDisplayName,
      getParticleStyle,
      sendMessage,
      clearChat,
      sendQuickMessage,
      onInputFocus,
      onInputBlur
    }
  }
}
</script>

<style scoped>
/* 动态背景粒子 */
.particles-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
  z-index: 0;
}

.particle {
  position: absolute;
  background: linear-gradient(45deg, #409EFF, #79bbff);
  border-radius: 50%;
  opacity: 0.6;
  animation: float 20s infinite linear;
}

@keyframes float {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 0.6;
  }
  90% {
    opacity: 0.6;
  }
  100% {
    transform: translateY(-100px) rotate(360deg);
    opacity: 0;
  }
}

.chat-container {
  padding: 20px;
  height: calc(100vh - 40px);
  background: linear-gradient(135deg, #f5f7fa 0%, #e8f4fd 50%, #f0f8ff 100%);
  min-height: 100vh;
  position: relative;
  overflow: hidden;
}

.chat-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  overflow: hidden;
  position: relative;
  z-index: 1;
}

.chat-header {
  background: linear-gradient(135deg, #409EFF 0%, #79bbff 100%);
  color: white;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  overflow: hidden;
}

.chat-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: headerShine 3s infinite;
}

@keyframes headerShine {
  0% { left: -100%; }
  100% { left: 100%; }
}

.header-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.ai-avatar {
  position: relative;
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
}

.avatar-glow {
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border-radius: 50%;
  background: linear-gradient(45deg, #409EFF, #79bbff, #409EFF);
  opacity: 0.5;
  animation: avatarGlow 2s ease-in-out infinite alternate;
  z-index: -1;
}

@keyframes avatarGlow {
  0% {
    transform: scale(1);
    opacity: 0.5;
  }
  100% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

.avatar-icon {
  font-size: 24px;
  z-index: 1;
}

.status-indicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 12px;
  height: 12px;
  background: #67C23A;
  border-radius: 50%;
  border: 2px solid white;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

.header-info h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
}

.status-text {
  margin: 0;
  font-size: 14px;
  opacity: 0.9;
  color: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  gap: 8px;
}

.typing-dots {
  display: flex;
  gap: 2px;
}

.typing-dots span {
  width: 4px;
  height: 4px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  animation: typingDots 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: 0s; }
.typing-dots span:nth-child(2) { animation-delay: 0.2s; }
.typing-dots span:nth-child(3) { animation-delay: 0.4s; }

@keyframes typingDots {
  0%, 60%, 100% {
    transform: translateY(0);
    opacity: 0.4;
  }
  30% {
    transform: translateY(-8px);
    opacity: 1;
  }
}

.clear-btn {
  color: white;
  font-size: 18px;
  transition: all 0.3s ease;
  border-radius: 8px;
  padding: 8px;
}

.clear-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  background: rgba(255, 255, 255, 0.5);
  position: relative;
}

.welcome-message {
  text-align: center;
  padding: 40px 20px;
  color: #606266;
  background: linear-gradient(135deg, #f8f9fa 0%, #e8f4fd 100%);
  border-radius: 16px;
  border-left: 4px solid #409EFF;
  position: relative;
  overflow: hidden;
}

.welcome-message::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(64, 158, 255, 0.1), transparent);
  animation: welcomeShine 4s infinite;
}

@keyframes welcomeShine {
  0% { left: -100%; }
  100% { left: 100%; }
}

.welcome-animation {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  margin-bottom: 16px;
}

.welcome-icon {
  font-size: 48px;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.floating-elements {
  display: flex;
  gap: 8px;
}

.float-element {
  font-size: 24px;
  animation: floatElement 3s ease-in-out infinite;
}

.float-element:nth-child(1) { animation-delay: 0s; }
.float-element:nth-child(2) { animation-delay: 0.5s; }
.float-element:nth-child(3) { animation-delay: 1s; }

@keyframes floatElement {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-15px) rotate(10deg);
  }
}

.welcome-message h4 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.welcome-message p {
  margin: 0 0 20px 0;
  font-size: 16px;
  line-height: 1.6;
  color: #606266;
}

.quick-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  flex-wrap: wrap;
}

.quick-action {
  cursor: pointer;
  padding: 12px 20px;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border: 1px solid #e4e7ed;
  border-radius: 20px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.quick-action:hover {
  background: linear-gradient(135deg, #409EFF 0%, #66b1ff 100%);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.action-icon {
  font-size: 16px;
}

.message {
  display: flex;
  align-items: flex-start;
  max-width: 85%;
  animation: messageSlideIn 0.6s ease-out;
  position: relative;
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.user-message {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.assistant-message {
  align-self: flex-start;
}

.message-content {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  width: 100%;
}

.user-message .message-content {
  flex-direction: row-reverse;
}

.message-avatar {
  flex-shrink: 0;
  position: relative;
}

.ai-avatar-small, .user-avatar-small {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  position: relative;
  z-index: 1;
}

.ai-avatar-small {
  background: linear-gradient(135deg, #409EFF 0%, #79bbff 100%);
  color: white;
}

.user-avatar-small {
  background: linear-gradient(135deg, #67C23A 0%, #85ce61 100%);
  color: white;
}

.avatar-ripple {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 50%;
  border: 2px solid;
  opacity: 0;
  animation: ripple 2s infinite;
}

.ai-avatar-small + .avatar-ripple {
  border-color: #409EFF;
}

.user-avatar-small + .avatar-ripple {
  border-color: #67C23A;
}

.avatar-ripple.active {
  animation: rippleActive 1.5s infinite;
}

@keyframes ripple {
  0% {
    transform: scale(1);
    opacity: 0.6;
  }
  100% {
    transform: scale(1.4);
    opacity: 0;
  }
}

@keyframes rippleActive {
  0% {
    transform: scale(1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1.6);
    opacity: 0;
  }
}

.message-bubble {
  flex: 1;
  min-width: 0;
  position: relative;
}

.message-text {
  padding: 16px 20px;
  border-radius: 16px;
  line-height: 1.6;
  font-size: 15px;
  word-wrap: break-word;
  position: relative;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
}

.message-text:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.user-message .message-text {
  background: linear-gradient(135deg, #409EFF 0%, #66b1ff 100%);
  color: white;
  border-bottom-right-radius: 6px;
}

.assistant-message .message-text {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border: 1px solid #e4e7ed;
  color: #303133;
  border-bottom-left-radius: 6px;
  border-left: 4px solid #409EFF;
}

.message-shine {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s;
}

.message-shine.active {
  animation: shine 2s infinite;
}

@keyframes shine {
  0% { left: -100%; }
  100% { left: 100%; }
}

.message-time {
  font-size: 12px;
  opacity: 0.7;
  margin-top: 8px;
  text-align: right;
}

.user-message .message-time {
  color: rgba(255, 255, 255, 0.8);
}

.assistant-message .message-time {
  color: #909399;
}

.typing-cursor {
  display: inline-block;
  width: 2px;
  height: 16px;
  background: #409EFF;
  margin-left: 4px;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

.typing-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 16px 20px;
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #409EFF;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) { animation-delay: 0s; }
.typing-indicator span:nth-child(2) { animation-delay: 0.2s; }
.typing-indicator span:nth-child(3) { animation-delay: 0.4s; }

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
    opacity: 0.5;
  }
  30% {
    transform: translateY(-10px);
    opacity: 1;
  }
}

.chat-input {
  padding: 20px 24px;
  background: rgba(255, 255, 255, 0.9);
  border-top: 1px solid rgba(228, 231, 237, 0.6);
  backdrop-filter: blur(10px);
}

.input-wrapper {
  display: flex;
  align-items: flex-end;
  gap: 12px;
}

.input-container {
  flex: 1;
  position: relative;
}

.message-input {
  flex: 1;
}

:deep(.el-textarea__inner) {
  border-radius: 20px;
  border: 2px solid #e4e7ed;
  padding: 12px 16px;
  font-size: 15px;
  resize: none;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
}

:deep(.el-textarea__inner:focus) {
  border-color: #409EFF;
  background: white;
  box-shadow: 0 0 0 4px rgba(64, 158, 255, 0.1);
}

.input-glow {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 22px;
  background: linear-gradient(45deg, #409EFF, #79bbff);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  z-index: -1;
}

.input-glow.active {
  opacity: 0.3;
  animation: inputGlow 2s infinite;
}

@keyframes inputGlow {
  0%, 100% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.6;
  }
}

.send-btn {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #409EFF 0%, #66b1ff 100%);
  border: none;
  border-radius: 50%;
  transition: all 0.3s ease;
  font-weight: 500;
  position: relative;
  overflow: hidden;
}

.send-btn:hover {
  background: linear-gradient(135deg, #66b1ff 0%, #409EFF 100%);
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 8px 20px rgba(64, 158, 255, 0.4);
}

.send-btn:disabled {
  opacity: 0.5;
  transform: none;
  box-shadow: none;
  background: #c0c4cc;
}

.btn-ripple {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.3s, height 0.3s;
}

.send-btn:active .btn-ripple {
  width: 100px;
  height: 100px;
}

/* 工具调用状态样式增强 */
.tool-call-status {
  background: linear-gradient(135deg, #f8f9fa 0%, #e8f4fd 100%);
  border: 1px solid #e4e7ed;
  border-radius: 12px;
  padding: 16px;
  border-left: 4px solid #409EFF;
  position: relative;
  overflow: hidden;
}

.tool-status-header {
  margin-bottom: 12px;
  position: relative;
}

.tool-status-message {
  font-weight: 600;
  color: #303133;
  font-size: 14px;
}

.status-wave {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, #409EFF, transparent);
  animation: wave 2s infinite;
}

@keyframes wave {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.tool-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tool-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.tool-item.pending {
  border-left: 4px solid #909399;
}

.tool-item.executing {
  border-left: 4px solid #409EFF;
  background: linear-gradient(135deg, #ecf5ff 0%, #e8f4fd 100%);
  animation: toolExecuting 1.5s infinite;
}

@keyframes toolExecuting {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

.tool-item.success {
  border-left: 4px solid #67C23A;
  background: linear-gradient(135deg, #f0f9ff 0%, #e8f5e8 100%);
}

.tool-item.error {
  border-left: 4px solid #F56C6C;
  background: linear-gradient(135deg, #fef0f0 0%, #fde2e2 100%);
}

.tool-icon {
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.executing-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.tool-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.tool-name {
  font-size: 13px;
  font-weight: 500;
  color: #303133;
}

.tool-error {
  font-size: 12px;
  color: #F56C6C;
  font-style: italic;
}

.tool-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 2px;
  background: #409EFF;
  animation: progress 2s infinite;
}

@keyframes progress {
  0% {
    width: 0%;
  }
  50% {
    width: 70%;
  }
  100% {
    width: 100%;
  }
}

/* 代码块样式增强 */
:deep(.message-text pre) {
  background: linear-gradient(135deg, #282c34 0%, #1e2329 100%);
  color: #abb2bf;
  padding: 16px;
  border-radius: 8px;
  overflow-x: auto;
  margin: 8px 0;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  border-left: 4px solid #409EFF;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

:deep(.message-text code) {
  background: linear-gradient(135deg, #f0f2f5 0%, #e8f0fe 100%);
  color: #e6a23c;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 14px;
}

/* 滚动条美化 */
.chat-messages::-webkit-scrollbar {
  width: 8px;
}

.chat-messages::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #409EFF 0%, #66b1ff 100%);
  border-radius: 4px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #337ecc 0%, #5c9dff 100%);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .message {
    max-width: 90%;
  }
}

@media (max-width: 992px) {
  .chat-container {
    padding: 15px;
  }
  
  .message {
    max-width: 95%;
  }
  
  .chat-input {
    padding: 16px;
  }
  
  .chat-messages {
    padding: 16px;
  }
  
  .quick-actions {
    flex-direction: column;
    align-items: center;
  }
}

@media (max-width: 768px) {
  .chat-container {
    padding: 10px;
  }
  
  .header-content {
    gap: 12px;
  }
  
  .ai-avatar {
    width: 40px;
    height: 40px;
  }
  
  .avatar-icon {
    font-size: 20px;
  }
  
  .header-info h3 {
    font-size: 16px;
  }
  
  .status-text {
    font-size: 12px;
  }
  
  .floating-elements {
    display: none;
  }
}

@media (max-width: 576px) {
  .message-text {
    padding: 12px 16px;
  }
  
  .ai-avatar-small, .user-avatar-small {
    width: 32px;
    height: 32px;
    font-size: 16px;
  }
  
  .send-btn {
    width: 44px;
    height: 44px;
  }
  
  .chat-wrapper {
    border-radius: 12px;
  }
}

/* 特殊状态动画 */
.streaming .message-text {
  animation: streamingPulse 1.5s infinite;
}

@keyframes streamingPulse {
  0%, 100% {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  50% {
    box-shadow: 0 4px 20px rgba(64, 158, 255, 0.3);
  }
}

.tool-calling .message-text {
  animation: toolCallingGlow 2s infinite;
}

@keyframes toolCallingGlow {
  0%, 100% {
    border-left-color: #409EFF;
  }
  50% {
    border-left-color: #66b1ff;
  }
}

.loading .avatar-ripple {
  animation: loadingRipple 1s infinite;
}

@keyframes loadingRipple {
  0% {
    transform: scale(1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}
</style>