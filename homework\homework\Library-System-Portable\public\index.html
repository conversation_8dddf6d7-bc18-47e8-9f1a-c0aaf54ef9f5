<!DOCTYPE html>
<html lang="">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <link rel="icon" href="<%= BASE_URL %>favicon.ico">
    <title>杭师大图书管理系统</title>

    <!-- Highcharts CDN -->
    <script src="https://code.highcharts.com/highcharts.js"></script>
    <script src="https://code.highcharts.com/highcharts-more.js"></script>
    <script src="https://code.highcharts.com/modules/solid-gauge.js"></script>
    <script src="https://code.highcharts.com/modules/exporting.js"></script>
    <script src="https://code.highcharts.com/modules/export-data.js"></script>

    <!-- 验证Highcharts加载并设置默认配置 -->
    <script>
      window.addEventListener('load', function() {
        console.log('页面加载完成，Highcharts状态:', !!window.Highcharts)
        if (window.Highcharts) {
          console.log('Highcharts版本:', window.Highcharts.version)

          // 设置Highcharts全局配置
          window.Highcharts.setOptions({
            lang: {
              loading: '加载中...',
              months: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'],
              shortMonths: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
              weekdays: ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'],
              decimalPoint: '.',
              thousandsSep: ','
            }
          })
        }
      })
    </script>
  </head>
  <body>
    <noscript>
      <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>
    <div id="app"></div>
    <!-- built files will be auto injected -->
  </body>
</html>
