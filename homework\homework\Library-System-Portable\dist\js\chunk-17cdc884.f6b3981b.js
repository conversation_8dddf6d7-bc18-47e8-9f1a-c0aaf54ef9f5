(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-17cdc884"],{"0dec":function(e,t,r){"use strict";r("d370")},a55b:function(e,t,r){"use strict";r.r(t);var a=r("7a23"),o=r("cf05"),c=r.n(o);const l={class:"login-container"},s={class:"login-box"},d={class:"login-left"},n={class:"login-features"},i={class:"feature"},b={class:"feature"},m={class:"feature"},u={class:"login-right"},j={class:"login-form-container"},O={class:"login-title"},p={class:"login-subtitle"},g={class:"form-actions"},V={class:"login-footer"};function f(e,t,r,o,f,w){const N=Object(a["resolveComponent"])("Reading"),h=Object(a["resolveComponent"])("el-icon"),x=Object(a["resolveComponent"])("Tickets"),C=Object(a["resolveComponent"])("User"),v=Object(a["resolveComponent"])("el-input"),_=Object(a["resolveComponent"])("el-form-item"),E=Object(a["resolveComponent"])("Lock"),F=Object(a["resolveComponent"])("el-checkbox"),k=Object(a["resolveComponent"])("el-button"),y=Object(a["resolveComponent"])("el-form"),R=Object(a["resolveComponent"])("el-tab-pane"),T=Object(a["resolveComponent"])("el-tabs");return Object(a["openBlock"])(),Object(a["createElementBlock"])("div",l,[Object(a["createElementVNode"])("div",s,[Object(a["createElementVNode"])("div",d,[t[10]||(t[10]=Object(a["createElementVNode"])("div",{class:"login-logo"},[Object(a["createElementVNode"])("img",{src:c.a,alt:"Logo",class:"logo-image"}),Object(a["createElementVNode"])("h1",null,"图书管理系统")],-1)),Object(a["createElementVNode"])("div",n,[Object(a["createElementVNode"])("div",i,[Object(a["createVNode"])(h,{class:"feature-icon"},{default:Object(a["withCtx"])(()=>[Object(a["createVNode"])(N)]),_:1}),t[7]||(t[7]=Object(a["createElementVNode"])("div",{class:"feature-text"},[Object(a["createElementVNode"])("h3",null,"图书管理"),Object(a["createElementVNode"])("p",null,"添加、编辑、删除图书信息")],-1))]),Object(a["createElementVNode"])("div",b,[Object(a["createVNode"])(h,{class:"feature-icon"},{default:Object(a["withCtx"])(()=>[Object(a["createVNode"])(x)]),_:1}),t[8]||(t[8]=Object(a["createElementVNode"])("div",{class:"feature-text"},[Object(a["createElementVNode"])("h3",null,"借阅管理"),Object(a["createElementVNode"])("p",null,"借阅、归还、续借、预约")],-1))]),Object(a["createElementVNode"])("div",m,[Object(a["createVNode"])(h,{class:"feature-icon"},{default:Object(a["withCtx"])(()=>[Object(a["createVNode"])(C)]),_:1}),t[9]||(t[9]=Object(a["createElementVNode"])("div",{class:"feature-text"},[Object(a["createElementVNode"])("h3",null,"用户管理"),Object(a["createElementVNode"])("p",null,"注册、登录、权限控制")],-1))])])]),Object(a["createElementVNode"])("div",u,[Object(a["createElementVNode"])("div",j,[Object(a["createElementVNode"])("h2",O,Object(a["toDisplayString"])("login"===o.activeTab?"欢迎回来":"创建账号"),1),Object(a["createElementVNode"])("p",p,Object(a["toDisplayString"])("login"===o.activeTab?"请登录您的账号":"请填写以下信息注册"),1),Object(a["createVNode"])(T,{modelValue:o.activeTab,"onUpdate:modelValue":t[6]||(t[6]=e=>o.activeTab=e),class:"login-tabs"},{default:Object(a["withCtx"])(()=>[Object(a["createVNode"])(R,{label:"登录",name:"login"},{default:Object(a["withCtx"])(()=>[Object(a["createVNode"])(y,{model:o.loginForm,rules:o.loginRules,ref:"loginFormRef","label-position":"top"},{default:Object(a["withCtx"])(()=>[Object(a["createVNode"])(_,{label:"用户名",prop:"username"},{default:Object(a["withCtx"])(()=>[Object(a["createVNode"])(v,{modelValue:o.loginForm.username,"onUpdate:modelValue":t[0]||(t[0]=e=>o.loginForm.username=e),placeholder:"请输入用户名"},{prefix:Object(a["withCtx"])(()=>[Object(a["createVNode"])(h,null,{default:Object(a["withCtx"])(()=>[Object(a["createVNode"])(C)]),_:1})]),_:1},8,["modelValue"])]),_:1}),Object(a["createVNode"])(_,{label:"密码",prop:"password"},{default:Object(a["withCtx"])(()=>[Object(a["createVNode"])(v,{modelValue:o.loginForm.password,"onUpdate:modelValue":t[1]||(t[1]=e=>o.loginForm.password=e),type:"password",placeholder:"请输入密码","show-password":""},{prefix:Object(a["withCtx"])(()=>[Object(a["createVNode"])(h,null,{default:Object(a["withCtx"])(()=>[Object(a["createVNode"])(E)]),_:1})]),_:1},8,["modelValue"])]),_:1}),Object(a["createElementVNode"])("div",g,[Object(a["createVNode"])(F,{modelValue:o.rememberMe,"onUpdate:modelValue":t[2]||(t[2]=e=>o.rememberMe=e)},{default:Object(a["withCtx"])(()=>t[11]||(t[11]=[Object(a["createTextVNode"])("记住我")])),_:1,__:[11]},8,["modelValue"]),t[12]||(t[12]=Object(a["createElementVNode"])("a",{href:"#",class:"forgot-password"},"忘记密码?",-1))]),Object(a["createVNode"])(_,null,{default:Object(a["withCtx"])(()=>[Object(a["createVNode"])(k,{type:"primary",onClick:o.handleLogin,class:"submit-button"},{default:Object(a["withCtx"])(()=>t[13]||(t[13]=[Object(a["createTextVNode"])("登录")])),_:1,__:[13]},8,["onClick"])]),_:1})]),_:1},8,["model","rules"])]),_:1}),Object(a["createVNode"])(R,{label:"注册",name:"register"},{default:Object(a["withCtx"])(()=>[Object(a["createVNode"])(y,{model:o.registerForm,rules:o.registerRules,ref:"registerFormRef","label-position":"top"},{default:Object(a["withCtx"])(()=>[Object(a["createVNode"])(_,{label:"用户名",prop:"username"},{default:Object(a["withCtx"])(()=>[Object(a["createVNode"])(v,{modelValue:o.registerForm.username,"onUpdate:modelValue":t[3]||(t[3]=e=>o.registerForm.username=e),placeholder:"请输入用户名"},{prefix:Object(a["withCtx"])(()=>[Object(a["createVNode"])(h,null,{default:Object(a["withCtx"])(()=>[Object(a["createVNode"])(C)]),_:1})]),_:1},8,["modelValue"])]),_:1}),Object(a["createVNode"])(_,{label:"密码",prop:"password"},{default:Object(a["withCtx"])(()=>[Object(a["createVNode"])(v,{modelValue:o.registerForm.password,"onUpdate:modelValue":t[4]||(t[4]=e=>o.registerForm.password=e),type:"password",placeholder:"请输入密码","show-password":""},{prefix:Object(a["withCtx"])(()=>[Object(a["createVNode"])(h,null,{default:Object(a["withCtx"])(()=>[Object(a["createVNode"])(E)]),_:1})]),_:1},8,["modelValue"])]),_:1}),Object(a["createVNode"])(_,{label:"确认密码",prop:"confirmPassword"},{default:Object(a["withCtx"])(()=>[Object(a["createVNode"])(v,{modelValue:o.registerForm.confirmPassword,"onUpdate:modelValue":t[5]||(t[5]=e=>o.registerForm.confirmPassword=e),type:"password",placeholder:"请再次输入密码","show-password":""},{prefix:Object(a["withCtx"])(()=>[Object(a["createVNode"])(h,null,{default:Object(a["withCtx"])(()=>[Object(a["createVNode"])(E)]),_:1})]),_:1},8,["modelValue"])]),_:1}),Object(a["createVNode"])(_,null,{default:Object(a["withCtx"])(()=>[Object(a["createVNode"])(k,{type:"primary",onClick:o.handleRegister,class:"submit-button"},{default:Object(a["withCtx"])(()=>t[14]||(t[14]=[Object(a["createTextVNode"])("注册")])),_:1,__:[14]},8,["onClick"])]),_:1})]),_:1},8,["model","rules"])]),_:1})]),_:1},8,["modelValue"]),Object(a["createElementVNode"])("div",V,[Object(a["createElementVNode"])("p",null,"© "+Object(a["toDisplayString"])((new Date).getFullYear())+" 图书管理系统. 保留所有权利.",1)])])])])])}r("d9e2"),r("14d9");var w=r("6605"),N=r("5502"),h=r("3ef4"),x=r("f6f2"),C={name:"LoginView",components:{Reading:x["Reading"],Tickets:x["Tickets"],User:x["User"],Lock:x["Lock"]},setup(){const e=Object(w["d"])(),t=Object(N["b"])(),r=Object(a["ref"])("login"),o=Object(a["ref"])(null),c=Object(a["ref"])(null),l=Object(a["ref"])(!1),s=Object(a["reactive"])({username:"",password:""}),d=Object(a["reactive"])({username:"",password:"",confirmPassword:""}),n={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:20,message:"长度在 3 到 20 个字符",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,max:20,message:"长度在 6 到 20 个字符",trigger:"blur"}]},i={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:20,message:"长度在 3 到 20 个字符",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,max:20,message:"长度在 6 到 20 个字符",trigger:"blur"}],confirmPassword:[{required:!0,message:"请再次输入密码",trigger:"blur"},{validator:(e,t,r)=>{t!==d.password?r(new Error("两次输入密码不一致")):r()},trigger:"blur"}]},b=()=>{o.value.validate(async r=>{if(!r)return!1;try{await t.dispatch("auth/login",{username:s.username,password:s.password}),h["a"].success("登录成功"),e.push("/")}catch(a){h["a"].error("登录失败: "+a.message)}})},m=()=>{c.value.validate(async e=>{if(!e)return!1;try{await t.dispatch("auth/register",{username:d.username,password:d.password}),h["a"].success("注册成功，请登录"),r.value="login",s.username=d.username,s.password="",d.username="",d.password="",d.confirmPassword=""}catch(a){h["a"].error("注册失败: "+a.message)}})};return{activeTab:r,loginForm:s,registerForm:d,loginRules:n,registerRules:i,loginFormRef:o,registerFormRef:c,rememberMe:l,handleLogin:b,handleRegister:m}}},v=(r("0dec"),r("6b0d")),_=r.n(v);const E=_()(C,[["render",f],["__scopeId","data-v-69fd7199"]]);t["default"]=E},d370:function(e,t,r){}}]);
//# sourceMappingURL=chunk-17cdc884.f6b3981b.js.map