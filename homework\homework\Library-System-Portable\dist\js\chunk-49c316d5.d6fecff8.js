(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-49c316d5"],{8724:function(e,t,c){"use strict";c("99e6")},"99e6":function(e,t,c){},e96f:function(e,t,c){"use strict";c.r(t);var n=c("7a23"),o=c("cf05"),a=c.n(o);const l={class:"admin-layout"},r={class:"header-content"},d={class:"user-info"};function s(e,t,c,o,s,i){const b=Object(n["resolveComponent"])("Reading"),u=Object(n["resolveComponent"])("el-icon"),O=Object(n["resolveComponent"])("el-menu-item"),j=Object(n["resolveComponent"])("Tickets"),m=Object(n["resolveComponent"])("User"),p=Object(n["resolveComponent"])("DataAnalysis"),f=Object(n["resolveComponent"])("el-menu"),N=Object(n["resolveComponent"])("el-aside"),V=Object(n["resolveComponent"])("el-button"),C=Object(n["resolveComponent"])("el-header"),_=Object(n["resolveComponent"])("router-view"),h=Object(n["resolveComponent"])("el-main"),v=Object(n["resolveComponent"])("el-footer"),w=Object(n["resolveComponent"])("el-container");return Object(n["openBlock"])(),Object(n["createElementBlock"])("div",l,[Object(n["createVNode"])(w,null,{default:Object(n["withCtx"])(()=>[Object(n["createVNode"])(N,{width:"200px"},{default:Object(n["withCtx"])(()=>[t[4]||(t[4]=Object(n["createElementVNode"])("div",{class:"logo-container"},[Object(n["createElementVNode"])("img",{alt:"Vue logo",src:a.a,class:"logo"}),Object(n["createElementVNode"])("h3",null,"图书管理系统"),Object(n["createElementVNode"])("div",{class:"admin-badge"},"管理员")],-1)),Object(n["createVNode"])(f,{router:"","default-active":o.activeIndex,class:"el-menu-vertical","background-color":"#545c64","text-color":"#fff","active-text-color":"#ffd04b"},{default:Object(n["withCtx"])(()=>[Object(n["createVNode"])(O,{index:"/admin/books"},{default:Object(n["withCtx"])(()=>[Object(n["createVNode"])(u,null,{default:Object(n["withCtx"])(()=>[Object(n["createVNode"])(b)]),_:1}),t[0]||(t[0]=Object(n["createElementVNode"])("span",null,"图书管理",-1))]),_:1,__:[0]}),Object(n["createVNode"])(O,{index:"/admin/borrowing"},{default:Object(n["withCtx"])(()=>[Object(n["createVNode"])(u,null,{default:Object(n["withCtx"])(()=>[Object(n["createVNode"])(j)]),_:1}),t[1]||(t[1]=Object(n["createElementVNode"])("span",null,"借阅管理",-1))]),_:1,__:[1]}),Object(n["createVNode"])(O,{index:"/admin/users"},{default:Object(n["withCtx"])(()=>[Object(n["createVNode"])(u,null,{default:Object(n["withCtx"])(()=>[Object(n["createVNode"])(m)]),_:1}),t[2]||(t[2]=Object(n["createElementVNode"])("span",null,"用户管理",-1))]),_:1,__:[2]}),Object(n["createVNode"])(O,{index:"/admin/statistics"},{default:Object(n["withCtx"])(()=>[Object(n["createVNode"])(u,null,{default:Object(n["withCtx"])(()=>[Object(n["createVNode"])(p)]),_:1}),t[3]||(t[3]=Object(n["createElementVNode"])("span",null,"数据统计",-1))]),_:1,__:[3]})]),_:1},8,["default-active"])]),_:1,__:[4]}),Object(n["createVNode"])(w,null,{default:Object(n["withCtx"])(()=>[Object(n["createVNode"])(C,null,{default:Object(n["withCtx"])(()=>[Object(n["createElementVNode"])("div",r,[Object(n["createElementVNode"])("h2",null,Object(n["toDisplayString"])(o.pageTitle),1),Object(n["createElementVNode"])("div",d,[Object(n["createElementVNode"])("span",null,"欢迎，"+Object(n["toDisplayString"])(o.currentUser.username),1),Object(n["createVNode"])(V,{type:"text",onClick:o.logout},{default:Object(n["withCtx"])(()=>t[5]||(t[5]=[Object(n["createTextVNode"])("退出登录")])),_:1,__:[5]},8,["onClick"])])])]),_:1}),Object(n["createVNode"])(h,null,{default:Object(n["withCtx"])(()=>[Object(n["createVNode"])(_)]),_:1}),Object(n["createVNode"])(v,null,{default:Object(n["withCtx"])(()=>[Object(n["createElementVNode"])("p",null,"图书管理系统 © "+Object(n["toDisplayString"])((new Date).getFullYear()),1)]),_:1})]),_:1})]),_:1})])}c("14d9");var i=c("6605"),b=c("5502"),u=c("3ef4"),O=c("f6f2"),j={name:"AdminLayout",components:{Reading:O["Reading"],Tickets:O["Tickets"],User:O["User"],DataAnalysis:O["DataAnalysis"]},setup(){const e=Object(i["c"])(),t=Object(i["d"])(),c=Object(b["b"])(),o=Object(n["computed"])(()=>e.path),a=Object(n["computed"])(()=>{const e=localStorage.getItem("user");return e?JSON.parse(e):{username:""}}),l=Object(n["computed"])(()=>{switch(e.path){case"/admin/books":return"图书管理";case"/admin/borrowing":return"借阅管理";case"/admin/users":return"用户管理";case"/admin/statistics":return"数据统计";default:return"图书管理系统"}}),r=async()=>{await c.dispatch("auth/logout"),u["a"].success("已退出登录"),t.push("/login")};return{activeIndex:o,pageTitle:l,currentUser:a,logout:r}}},m=(c("8724"),c("6b0d")),p=c.n(m);const f=p()(j,[["render",s],["__scopeId","data-v-2082f77e"]]);t["default"]=f}}]);
//# sourceMappingURL=chunk-49c316d5.d6fecff8.js.map