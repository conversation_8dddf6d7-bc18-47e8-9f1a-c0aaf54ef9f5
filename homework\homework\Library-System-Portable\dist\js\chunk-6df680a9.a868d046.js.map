{"version": 3, "sources": ["webpack:///./src/views/user/UserLayout.vue?076f", "webpack:///./src/views/user/UserLayout.vue", "webpack:///./src/views/user/UserLayout.vue?e540"], "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_container", "_component_el_aside", "width", "_createElementVNode", "alt", "src", "_imports_0", "_component_el_menu", "router", "default-active", "$setup", "activeIndex", "background-color", "text-color", "active-text-color", "_component_el_menu_item", "index", "_component_el_icon", "_component_Reading", "_component_Tickets", "_component_User", "_component_el_header", "_hoisted_2", "_toDisplayString", "pageTitle", "_hoisted_3", "currentUser", "username", "_component_el_button", "type", "onClick", "logout", "_cache", "_component_el_main", "_component_router_view", "_component_el_footer", "Date", "getFullYear", "name", "components", "Reading", "Tickets", "User", "setup", "route", "useRoute", "useRouter", "store", "useStore", "computed", "path", "userStr", "localStorage", "getItem", "JSON", "parse", "async", "dispatch", "ElMessage", "success", "removeItem", "push", "__exports__", "render"], "mappings": "kHAAA,W,kHCCOA,MAAM,e,GAgCEA,MAAM,kB,GAEJA,MAAM,a,+nBAlCrBC,gCAgDM,MAhDNC,EAgDM,CA/CJC,yBA8CeC,EAAA,M,6BA7Cb,IA2BW,CA3BXD,yBA2BWE,EAAA,CA3BDC,MAAM,SAAO,C,6BACrB,IAIM,C,YAJNC,gCAIM,OAJDP,MAAM,kBAAgB,CACzBO,gCAA6D,OAAxDC,IAAI,WAAWC,IAAAC,IAA4BV,MAAM,SACtDO,gCAAe,UAAX,UACJA,gCAAgC,OAA3BP,MAAM,cAAa,Q,IAE1BG,yBAoBUQ,EAAA,CAnBRC,OAAA,GACCC,iBAAgBC,EAAAC,YACjBf,MAAM,mBACNgB,mBAAiB,UACjBC,aAAW,OACXC,oBAAkB,W,8BAElB,IAGe,CAHff,yBAGegB,EAAA,CAHDC,MAAM,eAAa,C,6BAC/B,IAA8B,CAA9BjB,yBAA8BkB,EAAA,M,6BAArB,IAAW,CAAXlB,yBAAWmB,K,kBACpBf,gCAAiB,YAAX,QAAI,M,aAEZJ,yBAGegB,EAAA,CAHDC,MAAM,mBAAiB,C,6BACnC,IAA8B,CAA9BjB,yBAA8BkB,EAAA,M,6BAArB,IAAW,CAAXlB,yBAAWoB,K,kBACpBhB,gCAAiB,YAAX,QAAI,M,aAEZJ,yBAGegB,EAAA,CAHDC,MAAM,iBAAe,C,6BACjC,IAA2B,CAA3BjB,yBAA2BkB,EAAA,M,6BAAlB,IAAQ,CAARlB,yBAAQqB,K,kBACjBjB,gCAAiB,YAAX,QAAI,M,yDAIhBJ,yBAgBeC,EAAA,M,6BAfb,IAQY,CARZD,yBAQYsB,EAAA,M,6BAPV,IAMM,CANNlB,gCAMM,MANNmB,EAMM,CALJnB,gCAAwB,UAAAoB,6BAAjBb,EAAAc,WAAS,GAChBrB,gCAGM,MAHNsB,EAGM,CAFJtB,gCAA0C,YAApC,MAAGoB,6BAAGb,EAAAgB,YAAYC,UAAQ,GAChC5B,yBAAuD6B,EAAA,CAA5CC,KAAK,OAAQC,QAAOpB,EAAAqB,Q,8BAAQ,IAAIC,EAAA,KAAAA,EAAA,I,6BAAJ,W,uCAI7CjC,yBAEUkC,EAAA,M,6BADR,IAAe,CAAflC,yBAAemC,K,MAEjBnC,yBAEYoC,EAAA,M,6BADV,IAAmD,CAAnDhC,gCAAmD,SAAhD,YAAcoB,8BAAA,IAAOa,MAAOC,eAAW,K,sFAkBrC,GACbC,KAAM,aACNC,WAAY,CACVC,qBACAC,qBACAC,gBAEFC,QACE,MAAMC,EAAQC,iBACRrC,EAASsC,iBACTC,EAAQC,iBAERrC,EAAcsC,sBAAS,IAAML,EAAMM,MAGnCxB,EAAcuB,sBAAS,KAC3B,MAAME,EAAUC,aAAaC,QAAQ,QACrC,OAAOF,EAAUG,KAAKC,MAAMJ,GAAW,CAAExB,SAAU,MAG/CH,EAAYyB,sBAAS,KACzB,OAAQL,EAAMM,MACZ,IAAK,cACH,MAAO,OACT,IAAK,kBACH,MAAO,OACT,IAAK,gBACH,MAAO,OACT,QACE,MAAO,YAKPnB,EAASyB,gBACPT,EAAMU,SAAS,eACrBC,OAAUC,QAAQ,SAElBP,aAAaQ,WAAW,QAExBpD,EAAOqD,KAAK,WAGd,MAAO,CACLlD,cACAa,YACAE,cACAK,Y,iCCvGN,MAAM+B,EAA2B,IAAgB,EAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAE1E", "file": "js/chunk-6df680a9.a868d046.js", "sourcesContent": ["export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../../node_modules/vue-loader-v16/dist/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader-v16/dist/index.js??ref--1-1!./UserLayout.vue?vue&type=style&index=0&id=611a02ea&scoped=true&lang=css\"", "<template>\n  <div class=\"user-layout\">\n    <el-container>\n      <el-aside width=\"200px\">\n        <div class=\"logo-container\">\n          <img alt=\"Vue logo\" src=\"../../assets/logo.png\" class=\"logo\">\n          <h3>图书管理系统</h3>\n          <div class=\"user-badge\">读者</div>\n        </div>\n        <el-menu\n          router\n          :default-active=\"activeIndex\"\n          class=\"el-menu-vertical\"\n          background-color=\"#545c64\"\n          text-color=\"#fff\"\n          active-text-color=\"#ffd04b\"\n        >\n          <el-menu-item index=\"/user/books\">\n            <el-icon><Reading /></el-icon>\n            <span>图书查询</span>\n          </el-menu-item>\n          <el-menu-item index=\"/user/borrowing\">\n            <el-icon><Tickets /></el-icon>\n            <span>我的借阅</span>\n          </el-menu-item>\n          <el-menu-item index=\"/user/profile\">\n            <el-icon><User /></el-icon>\n            <span>个人中心</span>\n          </el-menu-item>\n        </el-menu>\n      </el-aside>\n      <el-container>\n        <el-header>\n          <div class=\"header-content\">\n            <h2>{{ pageTitle }}</h2>\n            <div class=\"user-info\">\n              <span>欢迎，{{ currentUser.username }}</span>\n              <el-button type=\"text\" @click=\"logout\">退出登录</el-button>\n            </div>\n          </div>\n        </el-header>\n        <el-main>\n          <router-view />\n        </el-main>\n        <el-footer>\n          <p>图书管理系统 &copy; {{ new Date().getFullYear() }}</p>\n        </el-footer>\n      </el-container>\n    </el-container>\n  </div>\n</template>\n\n<script>\nimport { computed } from 'vue'\nimport { useRoute, useRouter } from 'vue-router'\nimport { useStore } from 'vuex'\nimport { ElMessage } from 'element-plus'\nimport {\n  Reading,\n  Tickets,\n  User\n} from '@element-plus/icons-vue'\n\nexport default {\n  name: 'UserLayout',\n  components: {\n    Reading,\n    Tickets,\n    User\n  },\n  setup() {\n    const route = useRoute()\n    const router = useRouter()\n    const store = useStore()\n\n    const activeIndex = computed(() => route.path)\n\n    // 获取当前用户信息\n    const currentUser = computed(() => {\n      const userStr = localStorage.getItem('user')\n      return userStr ? JSON.parse(userStr) : { username: '' }\n    })\n\n    const pageTitle = computed(() => {\n      switch (route.path) {\n        case '/user/books':\n          return '图书查询'\n        case '/user/borrowing':\n          return '我的借阅'\n        case '/user/profile':\n          return '个人中心'\n        default:\n          return '图书管理系统'\n      }\n    })\n\n    // 退出登录\n    const logout = async () => {\n      await store.dispatch('auth/logout')\n      ElMessage.success('已退出登录')\n      // 确保清除localStorage中的用户信息\n      localStorage.removeItem('user')\n      // 重定向到登录页\n      router.push('/login')\n    }\n\n    return {\n      activeIndex,\n      pageTitle,\n      currentUser,\n      logout\n    }\n  }\n}\n</script>\n\n<style scoped>\n.user-layout {\n  height: 100vh;\n}\n\n.el-container {\n  height: 100%;\n}\n\n.el-aside {\n  background-color: #545c64;\n  color: white;\n}\n\n.el-header {\n  background-color: #f5f7fa;\n  border-bottom: 1px solid #e6e6e6;\n  display: flex;\n  align-items: center;\n}\n\n.el-footer {\n  background-color: #f5f7fa;\n  color: #909399;\n  text-align: center;\n  line-height: 60px;\n  border-top: 1px solid #e6e6e6;\n}\n\n.logo-container {\n  padding: 20px 0;\n  text-align: center;\n  position: relative;\n}\n\n.logo {\n  width: 60px;\n  height: 60px;\n}\n\n.user-badge {\n  position: absolute;\n  top: 10px;\n  right: 10px;\n  background-color: #409EFF;\n  color: white;\n  padding: 2px 8px;\n  border-radius: 10px;\n  font-size: 12px;\n}\n\n.header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  width: 100%;\n}\n\n.user-info {\n  display: flex;\n  align-items: center;\n}\n\n.user-info span {\n  margin-right: 10px;\n}\n\n.el-menu-vertical {\n  border-right: none;\n}\n</style>\n", "import { render } from \"./UserLayout.vue?vue&type=template&id=611a02ea&scoped=true\"\nimport script from \"./UserLayout.vue?vue&type=script&lang=js\"\nexport * from \"./UserLayout.vue?vue&type=script&lang=js\"\n\nimport \"./UserLayout.vue?vue&type=style&index=0&id=611a02ea&scoped=true&lang=css\"\n\nimport exportComponent from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\软件架构大作业\\\\homework\\\\node_modules\\\\vue-loader-v16\\\\dist\\\\exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-611a02ea\"]])\n\nexport default __exports__"], "sourceRoot": ""}