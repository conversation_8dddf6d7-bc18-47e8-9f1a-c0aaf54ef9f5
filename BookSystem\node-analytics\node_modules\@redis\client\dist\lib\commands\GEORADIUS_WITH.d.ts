import { RedisCommandArgument, RedisCommandArguments } from '.';
import { GeoReplyWith, GeoSearchOptions, GeoCoordinates, GeoUnits } from './generic-transformers';
export { FIRST_KEY_INDEX, IS_READ_ONLY } from './GEORADIUS';
export declare function transformArguments(key: RedisCommandArgument, coordinates: GeoCoordinates, radius: number, unit: GeoUnits, replyWith: Array<GeoReplyWith>, options?: GeoSearchOptions): RedisCommandArguments;
export { transformGeoMembersWithReply as transformReply } from './generic-transformers';
