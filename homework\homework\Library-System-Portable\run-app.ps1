# 杭师大图书管理系统启动脚本
Write-Host "启动杭师大图书管理系统..." -ForegroundColor Green
Write-Host ""

# 检查Node.js是否安装
try {
    $nodeVersion = node --version
    Write-Host "检测到Node.js版本: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "错误：未检测到Node.js，请先安装Node.js" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

# 检查npm是否可用
try {
    $npmVersion = npm --version
    Write-Host "检测到npm版本: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "错误：npm不可用，请检查Node.js安装" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

# 切换到脚本所在目录
Set-Location $PSScriptRoot

# 检查依赖是否安装
if (-not (Test-Path "node_modules")) {
    Write-Host "正在安装依赖..." -ForegroundColor Yellow
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Host "依赖安装失败，请检查网络连接" -ForegroundColor Red
        Read-Host "按任意键退出"
        exit 1
    }
}

# 构建应用
Write-Host "正在构建应用..." -ForegroundColor Yellow
npm run build
if ($LASTEXITCODE -ne 0) {
    Write-Host "构建失败" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

# 启动Electron应用
Write-Host "启动应用..." -ForegroundColor Green
npm run electron-prod

Read-Host "按任意键退出"
