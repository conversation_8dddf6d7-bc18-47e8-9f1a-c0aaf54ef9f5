(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-52ea0bcc"],{"215b":function(e,t,o){"use strict";var c=o("7a23");const a={ref:"chartContainer",class:"highcharts-container"};function r(e,t,o,r,n,s){return Object(c["openBlock"])(),Object(c["createElementBlock"])("div",a,null,512)}var n={name:"HighchartsWrapper",props:{options:{type:Object,required:!0}},setup(e){const t=Object(c["ref"])(null);let o=null;const a=()=>{if(console.log("尝试创建图表...",{hasHighcharts:!!window.Highcharts,hasContainer:!!t.value,optionsType:typeof e.options,optionsKeys:e.options?Object.keys(e.options):[]}),window.Highcharts&&t.value&&e.options)try{t.value.innerHTML="",o=window.Highcharts.chart(t.value,e.options),console.log("图表创建成功:",o)}catch(c){console.error("创建Highcharts图表失败:",c),console.error("错误详情:",c.message),console.error("图表配置:",e.options),t.value&&(t.value.innerHTML=`<div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #f56c6c; flex-direction: column;">\n              <div>图表创建失败</div>\n              <div style="font-size: 12px; margin-top: 5px;">${c.message}</div>\n            </div>`)}else console.warn("Highcharts未加载或容器不存在或配置为空"),t.value&&(t.value.innerHTML='<div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #666;">正在加载图表...</div>')},r=()=>{o&&(o.destroy(),o=null)};return Object(c["onMounted"])(()=>{console.log("HighchartsWrapper mounted");const e=(o=0)=>{window.Highcharts?a():o<10?(console.log(`Highcharts未加载，重试 ${o+1}/10`),setTimeout(()=>{e(o+1)},200)):(console.error("Highcharts加载失败，请检查CDN连接"),t.value&&(t.value.innerHTML='<div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #f56c6c;">图表加载失败，请检查网络连接</div>'))};e()}),Object(c["onUnmounted"])(()=>{r()}),Object(c["watch"])(()=>e.options,()=>{r(),a()},{deep:!0}),{chartContainer:t}}},s=(o("bb66"),o("6b0d")),i=o.n(s);const l=i()(n,[["render",r],["__scopeId","data-v-f326fe4a"]]);t["a"]=l},"25c4":function(e,t,o){},"4e4d":function(e,t,o){"use strict";o("25c4")},ab43:function(e,t,o){"use strict";var c=o("23e7"),a=o("c65b"),r=o("59ed"),n=o("825a"),s=o("46c4"),i=o("c5cc"),l=o("9bdd"),d=o("2a62"),b=o("f99f"),h=o("c430"),p=!h&&b("map",TypeError),u=i((function(){var e=this.iterator,t=n(a(this.next,e)),o=this.done=!!t.done;if(!o)return l(e,this.mapper,[t.value,this.counter++],!0)}));c({target:"Iterator",proto:!0,real:!0,forced:h||p},{map:function(e){n(this);try{r(e)}catch(t){d(this,"throw",t)}return p?a(p,this,e):new u(s(this),{mapper:e})}})},bb66:function(e,t,o){"use strict";o("df30")},df30:function(e,t,o){},fcd1:function(e,t,o){"use strict";o.r(t);var c=o("7a23");const a={class:"statistics-container"},r={class:"card-header"},n={class:"chart-container"},s={class:"card-header"},i={class:"chart-container"},l={class:"card-header"},d={class:"chart-container"},b={class:"card-header"},h={class:"chart-container"};function p(e,t,o,p,u,O){const j=Object(c["resolveComponent"])("el-button"),g=Object(c["resolveComponent"])("HighchartsWrapper"),m=Object(c["resolveComponent"])("el-card"),f=Object(c["resolveComponent"])("el-col"),x=Object(c["resolveComponent"])("el-row");return Object(c["openBlock"])(),Object(c["createElementBlock"])("div",a,[t[8]||(t[8]=Object(c["createElementVNode"])("h1",null,"数据统计",-1)),t[9]||(t[9]=Object(c["createElementVNode"])("p",{class:"page-description"}," 使用Highcharts展示图书管理系统的各项数据统计，提供丰富的交互功能和专业的数据可视化效果。 ",-1)),Object(c["createVNode"])(x,{gutter:20},{default:Object(c["withCtx"])(()=>[Object(c["createVNode"])(f,{span:24},{default:Object(c["withCtx"])(()=>[Object(c["createVNode"])(m,{class:"box-card"},{header:Object(c["withCtx"])(()=>[Object(c["createElementVNode"])("div",r,[t[1]||(t[1]=Object(c["createElementVNode"])("span",null,"图书分类统计 - Highcharts柱状图",-1)),Object(c["createVNode"])(j,{size:"small",onClick:p.exportCategoryChart},{default:Object(c["withCtx"])(()=>t[0]||(t[0]=[Object(c["createTextVNode"])("导出图表")])),_:1,__:[0]},8,["onClick"])])]),default:Object(c["withCtx"])(()=>[Object(c["createElementVNode"])("div",n,[Object(c["createVNode"])(g,{ref:"categoryChartRef",options:p.categoryChartOptions,class:"chart"},null,8,["options"])])]),_:1})]),_:1})]),_:1}),Object(c["createVNode"])(x,{gutter:20,class:"chart-row"},{default:Object(c["withCtx"])(()=>[Object(c["createVNode"])(f,{span:12},{default:Object(c["withCtx"])(()=>[Object(c["createVNode"])(m,{class:"box-card"},{header:Object(c["withCtx"])(()=>[Object(c["createElementVNode"])("div",s,[t[3]||(t[3]=Object(c["createElementVNode"])("span",null,"借阅分布 - Highcharts饼图",-1)),Object(c["createVNode"])(j,{size:"small",onClick:p.exportBorrowingChart},{default:Object(c["withCtx"])(()=>t[2]||(t[2]=[Object(c["createTextVNode"])("导出图表")])),_:1,__:[2]},8,["onClick"])])]),default:Object(c["withCtx"])(()=>[Object(c["createElementVNode"])("div",i,[Object(c["createVNode"])(g,{ref:"borrowingChartRef",options:p.borrowingPieChartOptions,class:"chart"},null,8,["options"])])]),_:1})]),_:1}),Object(c["createVNode"])(f,{span:12},{default:Object(c["withCtx"])(()=>[Object(c["createVNode"])(m,{class:"box-card"},{header:Object(c["withCtx"])(()=>[Object(c["createElementVNode"])("div",l,[t[5]||(t[5]=Object(c["createElementVNode"])("span",null,"借阅趋势 - Highcharts折线图",-1)),Object(c["createVNode"])(j,{size:"small",onClick:p.exportTrendChart},{default:Object(c["withCtx"])(()=>t[4]||(t[4]=[Object(c["createTextVNode"])("导出图表")])),_:1,__:[4]},8,["onClick"])])]),default:Object(c["withCtx"])(()=>[Object(c["createElementVNode"])("div",d,[Object(c["createVNode"])(g,{ref:"trendChartRef",options:p.borrowingTrendChartOptions,class:"chart"},null,8,["options"])])]),_:1})]),_:1})]),_:1}),Object(c["createVNode"])(x,{gutter:20,class:"chart-row"},{default:Object(c["withCtx"])(()=>[Object(c["createVNode"])(f,{span:24},{default:Object(c["withCtx"])(()=>[Object(c["createVNode"])(m,{class:"box-card"},{header:Object(c["withCtx"])(()=>[Object(c["createElementVNode"])("div",b,[t[7]||(t[7]=Object(c["createElementVNode"])("span",null,"库存与借阅对比 - Highcharts组合图表",-1)),Object(c["createVNode"])(j,{size:"small",onClick:p.exportStockChart},{default:Object(c["withCtx"])(()=>t[6]||(t[6]=[Object(c["createTextVNode"])("导出图表")])),_:1,__:[6]},8,["onClick"])])]),default:Object(c["withCtx"])(()=>[Object(c["createElementVNode"])("div",h,[Object(c["createVNode"])(g,{ref:"stockChartRef",options:p.stockComparisonChartOptions,class:"chart"},null,8,["options"])])]),_:1})]),_:1})]),_:1})])}o("e9f5"),o("7d54"),o("ab43");var u=o("5502"),O=o("3ef4"),j=o("215b"),g={name:"StatisticsView",components:{HighchartsWrapper:j["a"]},setup(){const e=Object(u["b"])(),t=Object(c["ref"])(null),o=Object(c["ref"])(null),a=Object(c["ref"])(null),r=Object(c["ref"])(null),n=Object(c["computed"])(()=>e.state.books),s=Object(c["computed"])(()=>e.getters.getBooksByCategory),i=Object(c["computed"])(()=>e.getters.getBorrowingStatistics),l=Object(c["computed"])(()=>{const e={};return n.value.forEach(t=>{e[t.category]||(e[t.category]={stock:0,borrowed:0}),e[t.category].stock+=t.stock,e[t.category].borrowed+=t.borrowed}),Object.entries(e).map(([e,t])=>({category:e,stock:t.stock,borrowed:t.borrowed,available:t.stock-t.borrowed}))}),d=Object(c["computed"])(()=>Math.max(...Object.values(s.value),1)),b=Object(c["computed"])(()=>Math.max(...Object.values(i.value),1)),h=Object(c["computed"])(()=>0===l.value.length?1:Math.max(...l.value.map(e=>e.stock),1)),p=()=>({credits:{enabled:!1},exporting:{enabled:!0,buttons:{contextButton:{menuItems:["viewFullscreen","printChart","separator","downloadPNG","downloadJPEG","downloadPDF","downloadSVG"]}}},responsive:{rules:[{condition:{maxWidth:500},chartOptions:{legend:{layout:"horizontal",align:"center",verticalAlign:"bottom"}}}]}}),j=Object(c["computed"])(()=>{const e=Object.keys(s.value),t=Object.values(s.value);console.log("柱状图数据:",{categories:e,data:t,categoryData:s.value});const o={chart:{type:"column",height:350},title:{text:"图书分类统计"},subtitle:{text:"展示各类别图书的数量分布"},xAxis:{categories:e.length>0?e:["暂无数据"]},yAxis:{title:{text:"图书数量"},min:0},series:[{name:"图书数量",data:t.length>0?t:[0],colorByPoint:!0}],credits:{enabled:!1},exporting:{enabled:!0},tooltip:{pointFormat:"<b>{point.y}</b> 本图书"}};return console.log("柱状图配置:",o),o}),g=Object(c["computed"])(()=>{const e=Object.entries(i.value),t=e.length>0?e.map(([e,t])=>({name:e,y:t})):[{name:"暂无数据",y:1}];return console.log("饼图数据:",{borrowingData:i.value,pieData:t}),{...p(),chart:{type:"pie",height:350},title:{text:"借阅分布统计",style:{fontSize:"16px"}},subtitle:{text:"各类别图书的借阅比例",style:{fontSize:"12px"}},series:[{name:"借阅数量",data:t,size:"80%",dataLabels:{enabled:!0,format:"<b>{point.name}</b>: {point.percentage:.1f}%"}}],plotOptions:{pie:{allowPointSelect:!0,cursor:"pointer",showInLegend:!0,dataLabels:{enabled:!0,distance:20}}},tooltip:{pointFormat:"<b>{point.y}</b> 次借阅 ({point.percentage:.1f}%)"}}}),m=Object(c["computed"])(()=>{console.log("折线图配置生成中...");const e={chart:{type:"line"},title:{text:"借阅趋势分析"},xAxis:{categories:["1月","2月","3月","4月","5月","6月"]},yAxis:{title:{text:"借阅次数"}},series:[{name:"科幻类",data:[10,15,12,18,22,25]},{name:"文学类",data:[8,12,16,14,20,18]}]};return console.log("折线图配置:",e),e}),f=Object(c["computed"])(()=>{console.log("组合图表配置生成中...");const e={title:{text:"图书库存与借阅对比分析"},xAxis:{categories:["科幻","历史","文学"]},yAxis:[{title:{text:"库存数量"}},{title:{text:"借阅次数"},opposite:!0}],series:[{name:"库存数量",type:"column",data:[120,90,150],yAxis:0},{name:"借阅次数",type:"line",data:[40,30,50],yAxis:1}]};return console.log("组合图表配置:",e),e}),x=()=>{O["a"].info("图表导出功能已集成在图表右上角菜单中")},C=()=>{O["a"].info("图表导出功能已集成在图表右上角菜单中")},w=()=>{O["a"].info("图表导出功能已集成在图表右上角菜单中")},v=()=>{O["a"].info("图表导出功能已集成在图表右上角菜单中")};return{categoryChartRef:t,borrowingChartRef:o,trendChartRef:a,stockChartRef:r,categoryData:s,borrowingData:i,stockData:l,maxCategoryValue:d,maxBorrowingValue:b,maxStockValue:h,categoryChartOptions:j,borrowingPieChartOptions:g,borrowingTrendChartOptions:m,stockComparisonChartOptions:f,exportCategoryChart:x,exportBorrowingChart:C,exportTrendChart:w,exportStockChart:v}}},m=(o("4e4d"),o("6b0d")),f=o.n(m);const x=f()(g,[["render",p],["__scopeId","data-v-ba9381b8"]]);t["default"]=x}}]);
//# sourceMappingURL=chunk-52ea0bcc.cce8b994.js.map