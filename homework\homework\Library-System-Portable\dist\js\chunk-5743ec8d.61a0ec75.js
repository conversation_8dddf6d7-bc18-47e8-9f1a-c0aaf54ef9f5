(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5743ec8d"],{"4a30":function(e,t,o){},"4de9":function(e,t,o){"use strict";o.r(t);var l=o("7a23");const c={class:"books-container"},a={class:"action-bar"},r={class:"dialog-footer"};function d(e,t,o,d,b,i){const n=Object(l["resolveComponent"])("el-button"),u=Object(l["resolveComponent"])("search"),s=Object(l["resolveComponent"])("el-icon"),j=Object(l["resolveComponent"])("el-input"),O=Object(l["resolveComponent"])("el-table-column"),m=Object(l["resolveComponent"])("el-tag"),V=Object(l["resolveComponent"])("el-table"),p=Object(l["resolveComponent"])("el-form-item"),h=Object(l["resolveComponent"])("el-input-number"),f=Object(l["resolveComponent"])("el-form"),w=Object(l["resolveComponent"])("el-dialog");return Object(l["openBlock"])(),Object(l["createElementBlock"])("div",c,[t[15]||(t[15]=Object(l["createElementVNode"])("h1",null,"图书管理",-1)),Object(l["createElementVNode"])("div",a,[Object(l["createVNode"])(n,{type:"primary",onClick:t[0]||(t[0]=e=>d.dialogVisible=!0)},{default:Object(l["withCtx"])(()=>t[10]||(t[10]=[Object(l["createTextVNode"])("添加图书")])),_:1,__:[10]}),Object(l["createVNode"])(j,{modelValue:d.searchQuery,"onUpdate:modelValue":t[1]||(t[1]=e=>d.searchQuery=e),placeholder:"搜索图书",class:"search-input",clearable:""},{prefix:Object(l["withCtx"])(()=>[Object(l["createVNode"])(s,null,{default:Object(l["withCtx"])(()=>[Object(l["createVNode"])(u)]),_:1})]),_:1},8,["modelValue"])]),Object(l["createVNode"])(V,{data:d.filteredBooks,style:{width:"100%"},border:""},{default:Object(l["withCtx"])(()=>[Object(l["createVNode"])(O,{prop:"id",label:"ID",width:"80"}),Object(l["createVNode"])(O,{prop:"title",label:"书名"}),Object(l["createVNode"])(O,{prop:"author",label:"作者"}),Object(l["createVNode"])(O,{prop:"category",label:"分类"}),Object(l["createVNode"])(O,{prop:"publisher",label:"出版社"}),Object(l["createVNode"])(O,{prop:"isbn",label:"ISBN"}),Object(l["createVNode"])(O,{label:"库存状态"},{default:Object(l["withCtx"])(e=>[Object(l["createVNode"])(m,{type:e.row.stock>e.row.borrowed?"success":"danger"},{default:Object(l["withCtx"])(()=>[Object(l["createTextVNode"])(Object(l["toDisplayString"])(e.row.borrowed)+"/"+Object(l["toDisplayString"])(e.row.stock),1)]),_:2},1032,["type"])]),_:1}),Object(l["createVNode"])(O,{label:"操作",width:"200"},{default:Object(l["withCtx"])(e=>[Object(l["createVNode"])(n,{size:"small",onClick:t=>d.editBook(e.row)},{default:Object(l["withCtx"])(()=>t[11]||(t[11]=[Object(l["createTextVNode"])("编辑")])),_:2,__:[11]},1032,["onClick"]),Object(l["createVNode"])(n,{size:"small",type:"danger",onClick:t=>d.confirmDelete(e.row.id)},{default:Object(l["withCtx"])(()=>t[12]||(t[12]=[Object(l["createTextVNode"])("删除")])),_:2,__:[12]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),Object(l["createVNode"])(w,{modelValue:d.dialogVisible,"onUpdate:modelValue":t[9]||(t[9]=e=>d.dialogVisible=e),title:d.isEditing?"编辑图书":"添加图书",width:"50%"},{footer:Object(l["withCtx"])(()=>[Object(l["createElementVNode"])("span",r,[Object(l["createVNode"])(n,{onClick:t[8]||(t[8]=e=>d.dialogVisible=!1)},{default:Object(l["withCtx"])(()=>t[13]||(t[13]=[Object(l["createTextVNode"])("取消")])),_:1,__:[13]}),Object(l["createVNode"])(n,{type:"primary",onClick:d.saveBook},{default:Object(l["withCtx"])(()=>t[14]||(t[14]=[Object(l["createTextVNode"])("确定")])),_:1,__:[14]},8,["onClick"])])]),default:Object(l["withCtx"])(()=>[Object(l["createVNode"])(f,{model:d.bookForm,"label-width":"120px"},{default:Object(l["withCtx"])(()=>[Object(l["createVNode"])(p,{label:"书名"},{default:Object(l["withCtx"])(()=>[Object(l["createVNode"])(j,{modelValue:d.bookForm.title,"onUpdate:modelValue":t[2]||(t[2]=e=>d.bookForm.title=e)},null,8,["modelValue"])]),_:1}),Object(l["createVNode"])(p,{label:"作者"},{default:Object(l["withCtx"])(()=>[Object(l["createVNode"])(j,{modelValue:d.bookForm.author,"onUpdate:modelValue":t[3]||(t[3]=e=>d.bookForm.author=e)},null,8,["modelValue"])]),_:1}),Object(l["createVNode"])(p,{label:"分类"},{default:Object(l["withCtx"])(()=>[Object(l["createVNode"])(j,{modelValue:d.bookForm.category,"onUpdate:modelValue":t[4]||(t[4]=e=>d.bookForm.category=e)},null,8,["modelValue"])]),_:1}),Object(l["createVNode"])(p,{label:"出版社"},{default:Object(l["withCtx"])(()=>[Object(l["createVNode"])(j,{modelValue:d.bookForm.publisher,"onUpdate:modelValue":t[5]||(t[5]=e=>d.bookForm.publisher=e)},null,8,["modelValue"])]),_:1}),Object(l["createVNode"])(p,{label:"ISBN"},{default:Object(l["withCtx"])(()=>[Object(l["createVNode"])(j,{modelValue:d.bookForm.isbn,"onUpdate:modelValue":t[6]||(t[6]=e=>d.bookForm.isbn=e)},null,8,["modelValue"])]),_:1}),Object(l["createVNode"])(p,{label:"库存数量"},{default:Object(l["withCtx"])(()=>[Object(l["createVNode"])(h,{modelValue:d.bookForm.stock,"onUpdate:modelValue":t[7]||(t[7]=e=>d.bookForm.stock=e),min:0},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}o("e9f5"),o("910d");var b=o("5502"),i=o("3ef4"),n=o("c9a1"),u=o("f6f2"),s={name:"BooksView",components:{Search:u["Search"]},setup(){const e=Object(b["b"])(),t=Object(l["ref"])(!1),o=Object(l["ref"])(!1),c=Object(l["ref"])(""),a=Object(l["ref"])({id:null,title:"",author:"",category:"",publisher:"",isbn:"",stock:1,borrowed:0}),r=Object(l["computed"])(()=>e.state.books),d=Object(l["computed"])(()=>{if(!c.value)return r.value;const e=c.value.toLowerCase();return r.value.filter(t=>t.title.toLowerCase().includes(e)||t.author.toLowerCase().includes(e)||t.category.toLowerCase().includes(e)||t.isbn.toLowerCase().includes(e))}),u=()=>{a.value={id:null,title:"",author:"",category:"",publisher:"",isbn:"",stock:1,borrowed:0}},s=e=>{o.value=!0,a.value={...e},t.value=!0},j=()=>{o.value?(e.dispatch("updateBook",a.value),i["a"].success("图书更新成功")):(e.dispatch("addBook",a.value),i["a"].success("图书添加成功")),t.value=!1,u(),o.value=!1},O=t=>{n["a"].confirm("确定要删除这本图书吗？此操作不可逆。","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{e.dispatch("deleteBook",t),i["a"].success("图书删除成功")}).catch(()=>{i["a"].info("已取消删除")})};return{dialogVisible:t,isEditing:o,bookForm:a,books:r,filteredBooks:d,searchQuery:c,editBook:s,saveBook:j,confirmDelete:O}}},j=(o("f95d"),o("6b0d")),O=o.n(j);const m=O()(s,[["render",d],["__scopeId","data-v-290f36eb"]]);t["default"]=m},f95d:function(e,t,o){"use strict";o("4a30")}}]);
//# sourceMappingURL=chunk-5743ec8d.61a0ec75.js.map