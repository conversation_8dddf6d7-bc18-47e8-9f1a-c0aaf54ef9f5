(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1dea58d1"],{a2be:function(e,t,o){},d624:function(e,t,o){"use strict";o("a2be")},ed81:function(e,t,o){"use strict";o.r(t);var c=o("7a23");const a={class:"users-container"},l={class:"action-bar"},r={key:0},d={class:"dialog-footer"};function n(e,t,o,n,b,s){const i=Object(c["resolveComponent"])("el-button"),u=Object(c["resolveComponent"])("search"),j=Object(c["resolveComponent"])("el-icon"),O=Object(c["resolveComponent"])("el-input"),m=Object(c["resolveComponent"])("el-table-column"),p=Object(c["resolveComponent"])("el-tag"),V=Object(c["resolveComponent"])("el-table"),w=Object(c["resolveComponent"])("el-form-item"),f=Object(c["resolveComponent"])("el-form"),h=Object(c["resolveComponent"])("el-dialog");return Object(c["openBlock"])(),Object(c["createElementBlock"])("div",a,[t[11]||(t[11]=Object(c["createElementVNode"])("h1",null,"用户管理",-1)),Object(c["createElementVNode"])("div",l,[Object(c["createVNode"])(i,{type:"primary",onClick:t[0]||(t[0]=e=>n.dialogVisible=!0)},{default:Object(c["withCtx"])(()=>t[6]||(t[6]=[Object(c["createTextVNode"])("添加用户")])),_:1,__:[6]}),Object(c["createVNode"])(O,{modelValue:n.searchQuery,"onUpdate:modelValue":t[1]||(t[1]=e=>n.searchQuery=e),placeholder:"搜索用户",class:"search-input",clearable:""},{prefix:Object(c["withCtx"])(()=>[Object(c["createVNode"])(j,null,{default:Object(c["withCtx"])(()=>[Object(c["createVNode"])(u)]),_:1})]),_:1},8,["modelValue"])]),Object(c["createVNode"])(V,{data:n.filteredUsers,style:{width:"100%"},border:""},{default:Object(c["withCtx"])(()=>[Object(c["createVNode"])(m,{prop:"id",label:"ID",width:"80"}),Object(c["createVNode"])(m,{prop:"name",label:"姓名"}),Object(c["createVNode"])(m,{prop:"contact",label:"联系方式"}),Object(c["createVNode"])(m,{label:"借阅数量"},{default:Object(c["withCtx"])(e=>[Object(c["createTextVNode"])(Object(c["toDisplayString"])(e.row.borrowedBooks.length),1)]),_:1}),Object(c["createVNode"])(m,{label:"借阅图书"},{default:Object(c["withCtx"])(e=>[(Object(c["openBlock"])(!0),Object(c["createElementBlock"])(c["Fragment"],null,Object(c["renderList"])(e.row.borrowedBooks,e=>(Object(c["openBlock"])(),Object(c["createBlock"])(p,{key:e,class:"book-tag"},{default:Object(c["withCtx"])(()=>[Object(c["createTextVNode"])(Object(c["toDisplayString"])(n.getBookTitle(e)),1)]),_:2},1024))),128)),0===e.row.borrowedBooks.length?(Object(c["openBlock"])(),Object(c["createElementBlock"])("span",r,"无借阅")):Object(c["createCommentVNode"])("",!0)]),_:1}),Object(c["createVNode"])(m,{label:"操作",width:"200"},{default:Object(c["withCtx"])(e=>[Object(c["createVNode"])(i,{size:"small",onClick:t=>n.editUser(e.row)},{default:Object(c["withCtx"])(()=>t[7]||(t[7]=[Object(c["createTextVNode"])("编辑")])),_:2,__:[7]},1032,["onClick"]),Object(c["createVNode"])(i,{size:"small",type:"danger",onClick:t=>n.confirmDelete(e.row.id),disabled:e.row.borrowedBooks.length>0},{default:Object(c["withCtx"])(()=>t[8]||(t[8]=[Object(c["createTextVNode"])("删除")])),_:2,__:[8]},1032,["onClick","disabled"])]),_:1})]),_:1},8,["data"]),Object(c["createVNode"])(h,{modelValue:n.dialogVisible,"onUpdate:modelValue":t[5]||(t[5]=e=>n.dialogVisible=e),title:n.isEditing?"编辑用户":"添加用户",width:"50%"},{footer:Object(c["withCtx"])(()=>[Object(c["createElementVNode"])("span",d,[Object(c["createVNode"])(i,{onClick:t[4]||(t[4]=e=>n.dialogVisible=!1)},{default:Object(c["withCtx"])(()=>t[9]||(t[9]=[Object(c["createTextVNode"])("取消")])),_:1,__:[9]}),Object(c["createVNode"])(i,{type:"primary",onClick:n.saveUser},{default:Object(c["withCtx"])(()=>t[10]||(t[10]=[Object(c["createTextVNode"])("确定")])),_:1,__:[10]},8,["onClick"])])]),default:Object(c["withCtx"])(()=>[Object(c["createVNode"])(f,{model:n.userForm,"label-width":"120px"},{default:Object(c["withCtx"])(()=>[Object(c["createVNode"])(w,{label:"姓名"},{default:Object(c["withCtx"])(()=>[Object(c["createVNode"])(O,{modelValue:n.userForm.name,"onUpdate:modelValue":t[2]||(t[2]=e=>n.userForm.name=e)},null,8,["modelValue"])]),_:1}),Object(c["createVNode"])(w,{label:"联系方式"},{default:Object(c["withCtx"])(()=>[Object(c["createVNode"])(O,{modelValue:n.userForm.contact,"onUpdate:modelValue":t[3]||(t[3]=e=>n.userForm.contact=e)},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}o("e9f5"),o("910d"),o("f665");var b=o("5502"),s=o("3ef4"),i=o("c9a1"),u=o("f6f2"),j={name:"UsersView",components:{Search:u["Search"]},setup(){const e=Object(b["b"])(),t=Object(c["ref"])(!1),o=Object(c["ref"])(!1),a=Object(c["ref"])(""),l=Object(c["ref"])({id:null,name:"",contact:"",borrowedBooks:[]}),r=Object(c["computed"])(()=>e.state.users),d=Object(c["computed"])(()=>e.state.books),n=Object(c["computed"])(()=>{if(!a.value)return r.value;const e=a.value.toLowerCase();return r.value.filter(t=>t.name.toLowerCase().includes(e)||t.contact.toLowerCase().includes(e))}),u=e=>{const t=d.value.find(t=>t.id===e);return t?t.title:"未知图书"},j=()=>{l.value={id:null,name:"",contact:"",borrowedBooks:[]}},O=e=>{o.value=!0,l.value={...e},t.value=!0},m=()=>{o.value?(e.dispatch("updateUser",l.value),s["a"].success("用户更新成功")):(e.dispatch("addUser",l.value),s["a"].success("用户添加成功")),t.value=!1,j(),o.value=!1},p=t=>{const o=r.value.find(e=>e.id===t);o.borrowedBooks.length>0?s["a"].warning("该用户有未归还的图书，无法删除"):i["a"].confirm("确定要删除这个用户吗？此操作不可逆。","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{e.dispatch("deleteUser",t),s["a"].success("用户删除成功")}).catch(()=>{s["a"].info("已取消删除")})};return{dialogVisible:t,isEditing:o,userForm:l,users:r,filteredUsers:n,searchQuery:a,getBookTitle:u,editUser:O,saveUser:m,confirmDelete:p}}},O=(o("d624"),o("6b0d")),m=o.n(O);const p=m()(j,[["render",n],["__scopeId","data-v-debab2f2"]]);t["default"]=p}}]);
//# sourceMappingURL=chunk-1dea58d1.97295288.js.map