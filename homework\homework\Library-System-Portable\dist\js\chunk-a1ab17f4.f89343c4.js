(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-a1ab17f4"],{9863:function(e,t,o){},ccc0:function(e,t,o){"use strict";o.r(t);var r=o("7a23");const l={class:"borrowing-container"},c={class:"action-bar"},a={class:"dialog-footer"};function b(e,t,o,b,d,n){const i=Object(r["resolveComponent"])("el-button"),u=Object(r["resolveComponent"])("search"),s=Object(r["resolveComponent"])("el-icon"),O=Object(r["resolveComponent"])("el-input"),j=Object(r["resolveComponent"])("el-table-column"),w=Object(r["resolveComponent"])("el-tag"),p=Object(r["resolveComponent"])("el-table"),m=Object(r["resolveComponent"])("el-option"),V=Object(r["resolveComponent"])("el-select"),f=Object(r["resolveComponent"])("el-form-item"),h=Object(r["resolveComponent"])("el-date-picker"),k=Object(r["resolveComponent"])("el-form"),C=Object(r["resolveComponent"])("el-dialog");return Object(r["openBlock"])(),Object(r["createElementBlock"])("div",l,[t[11]||(t[11]=Object(r["createElementVNode"])("h1",null,"借阅管理",-1)),Object(r["createElementVNode"])("div",c,[Object(r["createVNode"])(i,{type:"primary",onClick:t[0]||(t[0]=e=>b.dialogVisible=!0)},{default:Object(r["withCtx"])(()=>t[7]||(t[7]=[Object(r["createTextVNode"])("新增借阅")])),_:1,__:[7]}),Object(r["createVNode"])(O,{modelValue:b.searchQuery,"onUpdate:modelValue":t[1]||(t[1]=e=>b.searchQuery=e),placeholder:"搜索借阅记录",class:"search-input",clearable:""},{prefix:Object(r["withCtx"])(()=>[Object(r["createVNode"])(s,null,{default:Object(r["withCtx"])(()=>[Object(r["createVNode"])(u)]),_:1})]),_:1},8,["modelValue"])]),Object(r["createVNode"])(p,{data:b.filteredBorrowings,style:{width:"100%"},border:""},{default:Object(r["withCtx"])(()=>[Object(r["createVNode"])(j,{prop:"id",label:"ID",width:"80"}),Object(r["createVNode"])(j,{label:"图书"},{default:Object(r["withCtx"])(e=>[Object(r["createTextVNode"])(Object(r["toDisplayString"])(b.getBookTitle(e.row.bookId)),1)]),_:1}),Object(r["createVNode"])(j,{label:"借阅人"},{default:Object(r["withCtx"])(e=>[Object(r["createTextVNode"])(Object(r["toDisplayString"])(b.getUserName(e.row.userId)),1)]),_:1}),Object(r["createVNode"])(j,{prop:"borrowDate",label:"借阅日期"}),Object(r["createVNode"])(j,{prop:"returnDate",label:"归还日期"},{default:Object(r["withCtx"])(e=>[Object(r["createTextVNode"])(Object(r["toDisplayString"])(e.row.returnDate||"未归还"),1)]),_:1}),Object(r["createVNode"])(j,{label:"状态"},{default:Object(r["withCtx"])(e=>[Object(r["createVNode"])(w,{type:e.row.returnDate?"success":"warning"},{default:Object(r["withCtx"])(()=>[Object(r["createTextVNode"])(Object(r["toDisplayString"])(e.row.returnDate?"已归还":"借阅中"),1)]),_:2},1032,["type"])]),_:1}),Object(r["createVNode"])(j,{label:"操作",width:"120"},{default:Object(r["withCtx"])(e=>[e.row.returnDate?Object(r["createCommentVNode"])("",!0):(Object(r["openBlock"])(),Object(r["createBlock"])(i,{key:0,size:"small",type:"success",onClick:t=>b.returnBook(e.row.id)},{default:Object(r["withCtx"])(()=>t[8]||(t[8]=[Object(r["createTextVNode"])("归还")])),_:2,__:[8]},1032,["onClick"]))]),_:1})]),_:1},8,["data"]),Object(r["createVNode"])(C,{modelValue:b.dialogVisible,"onUpdate:modelValue":t[6]||(t[6]=e=>b.dialogVisible=e),title:"新增借阅",width:"50%"},{footer:Object(r["withCtx"])(()=>[Object(r["createElementVNode"])("span",a,[Object(r["createVNode"])(i,{onClick:t[5]||(t[5]=e=>b.dialogVisible=!1)},{default:Object(r["withCtx"])(()=>t[9]||(t[9]=[Object(r["createTextVNode"])("取消")])),_:1,__:[9]}),Object(r["createVNode"])(i,{type:"primary",onClick:b.saveBorrowing},{default:Object(r["withCtx"])(()=>t[10]||(t[10]=[Object(r["createTextVNode"])("确定")])),_:1,__:[10]},8,["onClick"])])]),default:Object(r["withCtx"])(()=>[Object(r["createVNode"])(k,{model:b.borrowingForm,"label-width":"120px"},{default:Object(r["withCtx"])(()=>[Object(r["createVNode"])(f,{label:"图书"},{default:Object(r["withCtx"])(()=>[Object(r["createVNode"])(V,{modelValue:b.borrowingForm.bookId,"onUpdate:modelValue":t[2]||(t[2]=e=>b.borrowingForm.bookId=e),placeholder:"选择图书",style:{width:"100%"}},{default:Object(r["withCtx"])(()=>[(Object(r["openBlock"])(!0),Object(r["createElementBlock"])(r["Fragment"],null,Object(r["renderList"])(b.availableBooks,e=>(Object(r["openBlock"])(),Object(r["createBlock"])(m,{key:e.id,label:e.title,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),Object(r["createVNode"])(f,{label:"借阅人"},{default:Object(r["withCtx"])(()=>[Object(r["createVNode"])(V,{modelValue:b.borrowingForm.userId,"onUpdate:modelValue":t[3]||(t[3]=e=>b.borrowingForm.userId=e),placeholder:"选择借阅人",style:{width:"100%"}},{default:Object(r["withCtx"])(()=>[(Object(r["openBlock"])(!0),Object(r["createElementBlock"])(r["Fragment"],null,Object(r["renderList"])(b.users,e=>(Object(r["openBlock"])(),Object(r["createBlock"])(m,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),Object(r["createVNode"])(f,{label:"借阅日期"},{default:Object(r["withCtx"])(()=>[Object(r["createVNode"])(h,{modelValue:b.borrowingForm.borrowDate,"onUpdate:modelValue":t[4]||(t[4]=e=>b.borrowingForm.borrowDate=e),type:"date",placeholder:"选择日期",style:{width:"100%"},format:"YYYY-MM-DD","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}o("e9f5"),o("910d"),o("f665");var d=o("5502"),n=o("3ef4"),i=o("f6f2"),u={name:"BorrowingView",components:{Search:i["Search"]},setup(){const e=Object(d["b"])(),t=Object(r["ref"])(!1),o=Object(r["ref"])(""),l=Object(r["ref"])({bookId:null,userId:null,borrowDate:(new Date).toISOString().split("T")[0]}),c=Object(r["computed"])(()=>e.state.books),a=Object(r["computed"])(()=>e.state.users),b=Object(r["computed"])(()=>e.state.borrowings),i=Object(r["computed"])(()=>c.value.filter(e=>e.stock>e.borrowed)),u=Object(r["computed"])(()=>{if(!o.value)return b.value;const e=o.value.toLowerCase();return b.value.filter(t=>{const o=s(t.bookId).toLowerCase(),r=O(t.userId).toLowerCase();return o.includes(e)||r.includes(e)})}),s=e=>{const t=c.value.find(t=>t.id===e);return t?t.title:"未知图书"},O=e=>{const t=a.value.find(t=>t.id===e);return t?t.name:"未知用户"},j=()=>{if(!l.value.bookId||!l.value.userId)return void n["a"].warning("请选择图书和借阅人");const o=e.dispatch("borrowBook",{bookId:l.value.bookId,userId:l.value.userId});o?(n["a"].success("借阅成功"),t.value=!1,l.value={bookId:null,userId:null,borrowDate:(new Date).toISOString().split("T")[0]}):n["a"].error("借阅失败，可能库存不足")},w=t=>{e.dispatch("returnBook",t),n["a"].success("图书归还成功")};return{dialogVisible:t,borrowingForm:l,books:c,users:a,borrowings:b,availableBooks:i,filteredBorrowings:u,searchQuery:o,getBookTitle:s,getUserName:O,saveBorrowing:j,returnBook:w}}},s=(o("e788"),o("6b0d")),O=o.n(s);const j=O()(u,[["render",b],["__scopeId","data-v-49ce15ca"]]);t["default"]=j},e788:function(e,t,o){"use strict";o("9863")}}]);
//# sourceMappingURL=chunk-a1ab17f4.f89343c4.js.map