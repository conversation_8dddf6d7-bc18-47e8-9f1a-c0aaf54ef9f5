{"version": 3, "sources": ["webpack:///./src/components/HighchartsWrapper.vue", "webpack:///./src/components/HighchartsWrapper.vue?0049", "webpack:///./src/views/Home.vue", "webpack:///./src/views/Home.vue?f63c", "webpack:///./src/components/HighchartsWrapper.vue?d869", "webpack:///./src/views/Home.vue?9444"], "names": ["ref", "class", "_createElementBlock", "_hoisted_1", "name", "props", "options", "type", "Object", "required", "setup", "chartContainer", "chart", "createChart", "console", "log", "hasHighcharts", "window", "Highcharts", "<PERSON><PERSON><PERSON><PERSON>", "value", "optionsType", "optionsKeys", "keys", "innerHTML", "error", "message", "warn", "destroy<PERSON>hart", "destroy", "onMounted", "checkAndCreateChart", "retryCount", "setTimeout", "onUnmounted", "watch", "deep", "__exports__", "render", "_createVNode", "_component_el_row", "gutter", "_component_el_col", "span", "_cache", "_createElementVNode", "_component_el_card", "header", "_withCtx", "_hoisted_2", "_toDisplayString", "$setup", "books", "length", "_hoisted_3", "users", "_hoisted_4", "borrowings", "_hoisted_5", "activeBorrowings", "_hoisted_6", "_component_el_tag", "size", "_hoisted_7", "_component_HighchartsWrapper", "categoryChartOptions", "_hoisted_8", "_hoisted_9", "gaugeChartOptions", "_hoisted_10", "_component_el_button", "onClick", "goToHighchartsDemo", "_hoisted_11", "_hoisted_12", "_component_el_icon", "_component_Trend<PERSON><PERSON>s", "_hoisted_13", "_component_Download", "_hoisted_14", "_component_Monitor", "_hoisted_15", "_component_Connection", "components", "HighchartsWrapper", "store", "useStore", "router", "useRouter", "computed", "state", "filter", "b", "returnDate", "categoryData", "getters", "getBooksByCategory", "borrowingData", "getBorrowingStatistics", "getBaseOptions", "credits", "enabled", "exporting", "responsive", "rules", "condition", "max<PERSON><PERSON><PERSON>", "chartOptions", "legend", "layout", "align", "verticalAlign", "height", "title", "text", "style", "fontSize", "xAxis", "categories", "labels", "yAxis", "min", "series", "data", "values", "colorByPoint", "colors", "plotOptions", "column", "dataLabels", "borderRadius", "tooltip", "pointFormat", "utilizationRate", "Math", "round", "pane", "center", "startAngle", "endAngle", "background", "backgroundColor", "innerRadius", "outerRadius", "shape", "max", "stops", "lineWidth", "tickWidth", "minorTickInterval", "tickAmount", "y", "format", "solidgauge", "borderWidth", "useHTML", "push"], "mappings": "2IACOA,IAAI,iBAAiBC,MAAM,wB,wDAAhCC,gCAA6D,MAA7DC,EAA6D,UAMhD,OACbC,KAAM,oBACNC,MAAO,CACLC,QAAS,CACPC,KAAMC,OACNC,UAAU,IAGdC,MAAML,GACJ,MAAMM,EAAiBX,iBAAI,MAC3B,IAAIY,EAAQ,KAEZ,MAAMC,EAAcA,KAQlB,GAPAC,QAAQC,IAAI,YAAa,CACvBC,gBAAiBC,OAAOC,WACxBC,eAAgBR,EAAeS,MAC/BC,mBAAoBhB,EAAMC,QAC1BgB,YAAajB,EAAMC,QAAUE,OAAOe,KAAKlB,EAAMC,SAAW,KAGxDW,OAAOC,YAAcP,EAAeS,OAASf,EAAMC,QACrD,IAEEK,EAAeS,MAAMI,UAAY,GAGjCZ,EAAQK,OAAOC,WAAWN,MAAMD,EAAeS,MAAOf,EAAMC,SAC5DQ,QAAQC,IAAI,UAAWH,GACvB,MAAOa,GACPX,QAAQW,MAAM,oBAAqBA,GACnCX,QAAQW,MAAM,QAASA,EAAMC,SAC7BZ,QAAQW,MAAM,QAASpB,EAAMC,SAGzBK,EAAeS,QACjBT,EAAeS,MAAMI,UAAY,mOAEkBC,EAAMC,0CAK7DZ,QAAQa,KAAK,4BAEThB,EAAeS,QACjBT,EAAeS,MAAMI,UAAY,yHAKjCI,EAAeA,KACfhB,IACFA,EAAMiB,UACNjB,EAAQ,OAqCZ,OAjCAkB,uBAAU,KACRhB,QAAQC,IAAI,6BAGZ,MAAMgB,EAAsBA,CAACC,EAAa,KACpCf,OAAOC,WACTL,IACSmB,EAAa,IACtBlB,QAAQC,IAAI,oBAAoBiB,EAAa,QAC7CC,WAAW,KACTF,EAAoBC,EAAa,IAChC,OAEHlB,QAAQW,MAAM,2BACVd,EAAeS,QACjBT,EAAeS,MAAMI,UAAY,kIAKvCO,MAGFG,yBAAY,KACVN,MAIFO,mBAAM,IAAM9B,EAAMC,QAAS,KACzBsB,IACAf,KACC,CAAEuB,MAAM,IAEJ,CACLzB,oB,iCC3FN,MAAM0B,EAA2B,IAAgB,EAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAE1E,U,2FCRRrC,MAAM,Q,GAgBEA,MAAM,gB,GAaNA,MAAM,gB,GAaNA,MAAM,gB,GAaNA,MAAM,gB,GAWJA,MAAM,e,GAKRA,MAAM,mB,GAYJA,MAAM,e,GAKRA,MAAM,mB,GAeJA,MAAM,e,GAORA,MAAM,qB,GAGAA,MAAM,gB,GAONA,MAAM,gB,GAONA,MAAM,gB,GAONA,MAAM,gB,wiBAtIzBC,gCAiJM,MAjJNC,EAiJM,CAhJJoC,yBAKSC,EAAA,CALAC,OAAQ,IAAE,C,6BACjB,IAGS,CAHTF,yBAGSG,EAAA,CAHAC,KAAM,IAAE,C,6BACf,IAAeC,EAAA,KAAAA,EAAA,IAAfC,gCAAe,UAAX,UAAM,GACVA,gCAAoC,SAAjC,iCAA6B,M,qBAIpCN,yBAoDSC,EAAA,CApDAC,OAAQ,GAAIxC,MAAM,a,8BACzB,IAWS,CAXTsC,yBAWSG,EAAA,CAXAC,KAAM,GAAC,C,6BACd,IASU,CATVJ,yBASUO,EAAA,CATD7C,MAAM,YAAU,CACZ8C,OAAMC,qBACf,IAEMJ,EAAA,KAAAA,EAAA,IAFNC,gCAEM,OAFD5C,MAAM,eAAa,CACtB4C,gCAAiB,YAAX,U,mCAGV,IAEM,CAFNA,gCAEM,MAFNI,EAEM,CADJJ,gCAA2B,UAAAK,6BAApBC,EAAAC,MAAMC,QAAM,O,cAKzBd,yBAWSG,EAAA,CAXAC,KAAM,GAAC,C,6BACd,IASU,CATVJ,yBASUO,EAAA,CATD7C,MAAM,YAAU,CACZ8C,OAAMC,qBACf,IAEMJ,EAAA,KAAAA,EAAA,IAFNC,gCAEM,OAFD5C,MAAM,eAAa,CACtB4C,gCAAiB,YAAX,U,mCAGV,IAEM,CAFNA,gCAEM,MAFNS,EAEM,CADJT,gCAA2B,UAAAK,6BAApBC,EAAAI,MAAMF,QAAM,O,cAKzBd,yBAWSG,EAAA,CAXAC,KAAM,GAAC,C,6BACd,IASU,CATVJ,yBASUO,EAAA,CATD7C,MAAM,YAAU,CACZ8C,OAAMC,qBACf,IAEMJ,EAAA,KAAAA,EAAA,IAFNC,gCAEM,OAFD5C,MAAM,eAAa,CACtB4C,gCAAiB,YAAX,U,mCAGV,IAEM,CAFNA,gCAEM,MAFNW,EAEM,CADJX,gCAAgC,UAAAK,6BAAzBC,EAAAM,WAAWJ,QAAM,O,cAK9Bd,yBAWSG,EAAA,CAXAC,KAAM,GAAC,C,6BACd,IASU,CATVJ,yBASUO,EAAA,CATD7C,MAAM,YAAU,CACZ8C,OAAMC,qBACf,IAEMJ,EAAA,KAAAA,EAAA,IAFNC,gCAEM,OAFD5C,MAAM,eAAa,CACtB4C,gCAAiB,YAAX,U,mCAGV,IAEM,CAFNA,gCAEM,MAFNa,EAEM,CADJb,gCAA+B,UAAAK,6BAAxBC,EAAAQ,kBAAgB,O,sBAM/BpB,yBAkCSC,EAAA,CAlCAC,OAAQ,GAAIxC,MAAM,c,8BACzB,IAeS,CAfTsC,yBAeSG,EAAA,CAfAC,KAAM,IAAE,C,6BACf,IAaU,CAbVJ,yBAaUO,EAAA,CAbD7C,MAAM,YAAU,CACZ8C,OAAMC,qBACf,IAGM,CAHNH,gCAGM,MAHNe,EAGM,C,YAFJf,gCAAgC,YAA1B,uBAAmB,IACzBN,yBAAiDsB,EAAA,CAAzCC,KAAK,QAAQvD,KAAK,W,8BAAU,IAAIqC,EAAA,KAAAA,EAAA,I,6BAAJ,W,8CAGxC,IAKM,CALNC,gCAKM,MALNkB,EAKM,CAJJxB,yBAGEyB,EAAA,CAFC1D,QAAS6C,EAAAc,qBACVhE,MAAM,S,sCAMdsC,yBAeSG,EAAA,CAfAC,KAAM,IAAE,C,6BACf,IAaU,CAbVJ,yBAaUO,EAAA,CAbD7C,MAAM,YAAU,CACZ8C,OAAMC,qBACf,IAGM,CAHNH,gCAGM,MAHNqB,EAGM,C,YAFJrB,gCAAiC,YAA3B,wBAAoB,IAC1BN,yBAAiDsB,EAAA,CAAzCC,KAAK,QAAQvD,KAAK,W,8BAAU,IAAIqC,EAAA,KAAAA,EAAA,I,6BAAJ,W,8CAGxC,IAKM,CALNC,gCAKM,MALNsB,EAKM,CAJJ5B,yBAGEyB,EAAA,CAFC1D,QAAS6C,EAAAiB,kBACVnE,MAAM,S,8CAQhBsC,yBA6CSC,EAAA,CA7CAC,OAAQ,GAAIxC,MAAM,c,8BACzB,IA2CS,CA3CTsC,yBA2CSG,EAAA,CA3CAC,KAAM,IAAE,C,6BACf,IAyCU,CAzCVJ,yBAyCUO,EAAA,CAzCD7C,MAAM,YAAU,CACZ8C,OAAMC,qBACf,IAKM,CALNH,gCAKM,MALNwB,EAKM,C,cAJJxB,gCAA6B,YAAvB,oBAAgB,IACtBN,yBAEY+B,EAAA,CAFDR,KAAK,QAAQvD,KAAK,UAAWgE,QAAOpB,EAAAqB,oB,8BAAoB,IAEnE5B,EAAA,KAAAA,EAAA,I,6BAFmE,e,4DAKvE,IA+BM,CA/BNC,gCA+BM,MA/BN4B,EA+BM,CA9BJlC,yBA6BSC,EAAA,CA7BAC,OAAQ,IAAE,C,6BACjB,IAMS,CANTF,yBAMSG,EAAA,CANAC,KAAM,GAAC,C,6BACd,IAIM,CAJNE,gCAIM,MAJN6B,EAIM,CAHJnC,yBAAuDoC,EAAA,CAA9C1E,MAAM,gBAAc,C,6BAAC,IAAe,CAAfsC,yBAAeqC,K,oBAC7C/B,gCAAe,UAAX,UAAM,I,cACVA,gCAAqC,SAAlC,kCAA8B,Q,MAGrCN,yBAMSG,EAAA,CANAC,KAAM,GAAC,C,6BACd,IAIM,CAJNE,gCAIM,MAJNgC,EAIM,CAHJtC,yBAAoDoC,EAAA,CAA3C1E,MAAM,gBAAc,C,6BAAC,IAAY,CAAZsC,yBAAYuC,K,oBAC1CjC,gCAAa,UAAT,QAAI,I,cACRA,gCAAoC,SAAjC,iCAA6B,Q,MAGpCN,yBAMSG,EAAA,CANAC,KAAM,GAAC,C,6BACd,IAIM,CAJNE,gCAIM,MAJNkC,EAIM,CAHJxC,yBAAmDoC,EAAA,CAA1C1E,MAAM,gBAAc,C,6BAAC,IAAW,CAAXsC,yBAAWyC,K,oBACzCnC,gCAAc,UAAV,SAAK,I,cACTA,gCAA4B,SAAzB,yBAAqB,Q,MAG5BN,yBAMSG,EAAA,CANAC,KAAM,GAAC,C,6BACd,IAIM,CAJNE,gCAIM,MAJNoC,EAIM,CAHJ1C,yBAAsDoC,EAAA,CAA7C1E,MAAM,gBAAc,C,6BAAC,IAAc,CAAdsC,yBAAc2C,K,oBAC5CrC,gCAAa,UAAT,QAAI,I,cACRA,gCAA2B,SAAxB,wBAAoB,Q,gHAiB1B,GACbzC,KAAM,WACN+E,WAAY,CACVC,0BAEF1E,QACE,MAAM2E,EAAQC,iBACRC,EAASC,iBAETpC,EAAQqC,sBAAS,IAAMJ,EAAMK,MAAMtC,OACnCG,EAAQkC,sBAAS,IAAMJ,EAAMK,MAAMnC,OACnCE,EAAagC,sBAAS,IAAMJ,EAAMK,MAAMjC,YACxCE,EAAmB8B,sBAAS,IAChChC,EAAWrC,MAAMuE,OAAOC,GAAsB,OAAjBA,EAAEC,YAAqBxC,QAGhDyC,EAAeL,sBAAS,IACrBJ,EAAMU,QAAQC,oBAGjBC,EAAgBR,sBAAS,IACtBJ,EAAMU,QAAQG,wBAIjBC,EAAiBA,KAAA,CACrBC,QAAS,CAAEC,SAAS,GACpBC,UAAW,CAAED,SAAS,GACtBE,WAAY,CACVC,MAAO,CAAC,CACNC,UAAW,CAAEC,SAAU,KACvBC,aAAc,CACZC,OAAQ,CAAEC,OAAQ,aAAcC,MAAO,SAAUC,cAAe,gBAOlE9C,EAAuBwB,sBAAS,KAAM,IACvCU,IACHvF,MAAO,CACLL,KAAM,SACNyG,OAAQ,KAEVC,MAAO,CACLC,KAAM,SACNC,MAAO,CAAEC,SAAU,SAErBC,MAAO,CACLC,WAAY9G,OAAOe,KAAKuE,EAAa1E,OACrCmG,OAAQ,CAAEJ,MAAO,CAAEC,SAAU,UAE/BI,MAAO,CACLP,MAAO,CACLC,KAAM,KACNC,MAAO,CAAEC,SAAU,SAErBK,IAAK,EACLF,OAAQ,CAAEJ,MAAO,CAAEC,SAAU,UAE/BM,OAAQ,CAAC,CACPtH,KAAM,OACNuH,KAAMnH,OAAOoH,OAAO9B,EAAa1E,OACjCyG,cAAc,EACdC,OAAQ,CAAC,UAAW,UAAW,UAAW,UAAW,UAAW,aAElEC,YAAa,CACXC,OAAQ,CACNC,WAAY,CACV5B,SAAS,EACTc,MAAO,CAAEC,SAAU,SAErBc,aAAc,IAGlBtB,OAAQ,CAAEP,SAAS,GACnB8B,QAAS,CACPC,YAAa,2BAKXhE,EAAoBqB,sBAAS,KACjC,MAAM4C,EAAkBC,KAAKC,MAAO5E,EAAiBvC,MAAQgC,EAAMhC,MAAMiC,OAAU,KAEnF,MAAO,IACF8C,IACHvF,MAAO,CACLL,KAAM,aACNyG,OAAQ,KAEVC,MAAO,CACLC,KAAM,QACNC,MAAO,CAAEC,SAAU,SAErBoB,KAAM,CACJC,OAAQ,CAAC,MAAO,OAChB3E,KAAM,OACN4E,YAAa,GACbC,SAAU,GACVC,WAAY,CACVC,gBAAiB,OACjBC,YAAa,MACbC,YAAa,OACbC,MAAO,QAGXxB,MAAO,CACLC,IAAK,EACLwB,IAAK,IACLC,MAAO,CACL,CAAC,GAAK,WACN,CAAC,GAAK,WACN,CAAC,GAAK,YAERC,UAAW,EACXC,UAAW,EACXC,kBAAmB,KACnBC,WAAY,EACZrC,MAAO,CACLsC,GAAI,GACJrC,KAAM,UACNC,MAAO,CAAEC,SAAU,SAErBG,OAAQ,CACNgC,EAAG,GACHpC,MAAO,CAAEC,SAAU,UAGvBM,OAAQ,CAAC,CACPtH,KAAM,MACNuH,KAAM,CAACU,GACPJ,WAAY,CACVuB,OAAQ,wIAGZzB,YAAa,CACX0B,WAAY,CACVxB,WAAY,CACVsB,EAAG,EACHG,YAAa,EACbC,SAAS,QAQbnF,EAAqBA,KACzBe,EAAOqE,KAAK,sBAGd,MAAO,CACLxG,QACAG,QACAE,aACAE,mBACAmC,eACAG,gBACAhC,uBACAG,oBACAI,wB,iCCvTN,MAAMnC,EAA2B,IAAgB,EAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAE1E,gB,kCCTf,W,kCCAA,W", "file": "js/chunk-ec892d4a.d34f040a.js", "sourcesContent": ["<template>\n  <div ref=\"chartContainer\" class=\"highcharts-container\"></div>\n</template>\n\n<script>\nimport { ref, onMounted, onUnmounted, watch } from 'vue'\n\nexport default {\n  name: 'HighchartsWrapper',\n  props: {\n    options: {\n      type: Object,\n      required: true\n    }\n  },\n  setup(props) {\n    const chartContainer = ref(null)\n    let chart = null\n\n    const createChart = () => {\n      console.log('尝试创建图表...', {\n        hasHighcharts: !!window.Highcharts,\n        hasContainer: !!chartContainer.value,\n        optionsType: typeof props.options,\n        optionsKeys: props.options ? Object.keys(props.options) : []\n      })\n\n      if (window.Highcharts && chartContainer.value && props.options) {\n        try {\n          // 清空容器\n          chartContainer.value.innerHTML = ''\n\n          // 创建图表\n          chart = window.Highcharts.chart(chartContainer.value, props.options)\n          console.log('图表创建成功:', chart)\n        } catch (error) {\n          console.error('创建Highcharts图表失败:', error)\n          console.error('错误详情:', error.message)\n          console.error('图表配置:', props.options)\n\n          // 显示错误信息\n          if (chartContainer.value) {\n            chartContainer.value.innerHTML = `<div style=\"display: flex; align-items: center; justify-content: center; height: 100%; color: #f56c6c; flex-direction: column;\">\n              <div>图表创建失败</div>\n              <div style=\"font-size: 12px; margin-top: 5px;\">${error.message}</div>\n            </div>`\n          }\n        }\n      } else {\n        console.warn('Highcharts未加载或容器不存在或配置为空')\n        // 如果Highcharts还没加载，显示提示信息\n        if (chartContainer.value) {\n          chartContainer.value.innerHTML = '<div style=\"display: flex; align-items: center; justify-content: center; height: 100%; color: #666;\">正在加载图表...</div>'\n        }\n      }\n    }\n\n    const destroyChart = () => {\n      if (chart) {\n        chart.destroy()\n        chart = null\n      }\n    }\n\n    onMounted(() => {\n      console.log('HighchartsWrapper mounted')\n\n      // 检查Highcharts是否已加载的函数\n      const checkAndCreateChart = (retryCount = 0) => {\n        if (window.Highcharts) {\n          createChart()\n        } else if (retryCount < 10) {\n          console.log(`Highcharts未加载，重试 ${retryCount + 1}/10`)\n          setTimeout(() => {\n            checkAndCreateChart(retryCount + 1)\n          }, 200)\n        } else {\n          console.error('Highcharts加载失败，请检查CDN连接')\n          if (chartContainer.value) {\n            chartContainer.value.innerHTML = '<div style=\"display: flex; align-items: center; justify-content: center; height: 100%; color: #f56c6c;\">图表加载失败，请检查网络连接</div>'\n          }\n        }\n      }\n\n      checkAndCreateChart()\n    })\n\n    onUnmounted(() => {\n      destroyChart()\n    })\n\n    // 监听options变化\n    watch(() => props.options, () => {\n      destroyChart()\n      createChart()\n    }, { deep: true })\n\n    return {\n      chartContainer\n    }\n  }\n}\n</script>\n\n<style scoped>\n.highcharts-container {\n  width: 100%;\n  height: 100%;\n}\n</style>\n", "import { render } from \"./HighchartsWrapper.vue?vue&type=template&id=f326fe4a&scoped=true\"\nimport script from \"./HighchartsWrapper.vue?vue&type=script&lang=js\"\nexport * from \"./HighchartsWrapper.vue?vue&type=script&lang=js\"\n\nimport \"./HighchartsWrapper.vue?vue&type=style&index=0&id=f326fe4a&scoped=true&lang=css\"\n\nimport exportComponent from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\软件架构大作业\\\\homework\\\\node_modules\\\\vue-loader-v16\\\\dist\\\\exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-f326fe4a\"]])\n\nexport default __exports__", "<template>\n  <div class=\"home\">\n    <el-row :gutter=\"20\">\n      <el-col :span=\"24\">\n        <h1>图书管理系统</h1>\n        <p>欢迎使用图书管理系统，您可以通过导航菜单访问各个功能模块。</p>\n      </el-col>\n    </el-row>\n\n    <el-row :gutter=\"20\" class=\"dashboard\">\n      <el-col :span=\"6\">\n        <el-card class=\"box-card\">\n          <template #header>\n            <div class=\"card-header\">\n              <span>图书总数</span>\n            </div>\n          </template>\n          <div class=\"card-content\">\n            <h2>{{ books.length }}</h2>\n          </div>\n        </el-card>\n      </el-col>\n\n      <el-col :span=\"6\">\n        <el-card class=\"box-card\">\n          <template #header>\n            <div class=\"card-header\">\n              <span>用户总数</span>\n            </div>\n          </template>\n          <div class=\"card-content\">\n            <h2>{{ users.length }}</h2>\n          </div>\n        </el-card>\n      </el-col>\n\n      <el-col :span=\"6\">\n        <el-card class=\"box-card\">\n          <template #header>\n            <div class=\"card-header\">\n              <span>借阅总数</span>\n            </div>\n          </template>\n          <div class=\"card-content\">\n            <h2>{{ borrowings.length }}</h2>\n          </div>\n        </el-card>\n      </el-col>\n\n      <el-col :span=\"6\">\n        <el-card class=\"box-card\">\n          <template #header>\n            <div class=\"card-header\">\n              <span>当前借出</span>\n            </div>\n          </template>\n          <div class=\"card-content\">\n            <h2>{{ activeBorrowings }}</h2>\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <el-row :gutter=\"20\" class=\"charts-row\">\n      <el-col :span=\"12\">\n        <el-card class=\"box-card\">\n          <template #header>\n            <div class=\"card-header\">\n              <span>图书分类统计 - Highcharts</span>\n              <el-tag size=\"small\" type=\"success\">实时数据</el-tag>\n            </div>\n          </template>\n          <div class=\"chart-container\">\n            <HighchartsWrapper\n              :options=\"categoryChartOptions\"\n              class=\"chart\"\n            />\n          </div>\n        </el-card>\n      </el-col>\n\n      <el-col :span=\"12\">\n        <el-card class=\"box-card\">\n          <template #header>\n            <div class=\"card-header\">\n              <span>系统状态仪表盘 - Highcharts</span>\n              <el-tag size=\"small\" type=\"warning\">动态更新</el-tag>\n            </div>\n          </template>\n          <div class=\"chart-container\">\n            <HighchartsWrapper\n              :options=\"gaugeChartOptions\"\n              class=\"chart\"\n            />\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <!-- 新增的Highcharts功能展示区域 -->\n    <el-row :gutter=\"20\" class=\"charts-row\">\n      <el-col :span=\"24\">\n        <el-card class=\"box-card\">\n          <template #header>\n            <div class=\"card-header\">\n              <span>Highcharts功能特色展示</span>\n              <el-button size=\"small\" type=\"primary\" @click=\"goToHighchartsDemo\">\n                查看完整演示\n              </el-button>\n            </div>\n          </template>\n          <div class=\"features-showcase\">\n            <el-row :gutter=\"20\">\n              <el-col :span=\"6\">\n                <div class=\"feature-item\">\n                  <el-icon class=\"feature-icon\"><TrendCharts /></el-icon>\n                  <h4>多种图表类型</h4>\n                  <p>支持柱状图、饼图、折线图、面积图、仪表盘、热力图、3D图表等</p>\n                </div>\n              </el-col>\n              <el-col :span=\"6\">\n                <div class=\"feature-item\">\n                  <el-icon class=\"feature-icon\"><Download /></el-icon>\n                  <h4>导出功能</h4>\n                  <p>支持导出PNG、JPEG、PDF、SVG格式，以及打印功能</p>\n                </div>\n              </el-col>\n              <el-col :span=\"6\">\n                <div class=\"feature-item\">\n                  <el-icon class=\"feature-icon\"><Monitor /></el-icon>\n                  <h4>响应式设计</h4>\n                  <p>自适应各种屏幕尺寸，支持移动设备和触摸操作</p>\n                </div>\n              </el-col>\n              <el-col :span=\"6\">\n                <div class=\"feature-item\">\n                  <el-icon class=\"feature-icon\"><Connection /></el-icon>\n                  <h4>交互功能</h4>\n                  <p>丰富的鼠标交互、数据钻取、缩放平移等功能</p>\n                </div>\n              </el-col>\n            </el-row>\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n  </div>\n</template>\n\n<script>\nimport { computed } from 'vue'\nimport { useStore } from 'vuex'\nimport { useRouter } from 'vue-router'\nimport HighchartsWrapper from '@/components/HighchartsWrapper.vue'\n\nexport default {\n  name: 'HomeView',\n  components: {\n    HighchartsWrapper\n  },\n  setup() {\n    const store = useStore()\n    const router = useRouter()\n\n    const books = computed(() => store.state.books)\n    const users = computed(() => store.state.users)\n    const borrowings = computed(() => store.state.borrowings)\n    const activeBorrowings = computed(() =>\n      borrowings.value.filter(b => b.returnDate === null).length\n    )\n\n    const categoryData = computed(() => {\n      return store.getters.getBooksByCategory\n    })\n\n    const borrowingData = computed(() => {\n      return store.getters.getBorrowingStatistics\n    })\n\n    // Highcharts基础配置\n    const getBaseOptions = () => ({\n      credits: { enabled: false },\n      exporting: { enabled: false },\n      responsive: {\n        rules: [{\n          condition: { maxWidth: 500 },\n          chartOptions: {\n            legend: { layout: 'horizontal', align: 'center', verticalAlign: 'bottom' }\n          }\n        }]\n      }\n    })\n\n    // 图书分类统计图表配置\n    const categoryChartOptions = computed(() => ({\n      ...getBaseOptions(),\n      chart: {\n        type: 'column',\n        height: 250\n      },\n      title: {\n        text: '图书分类分布',\n        style: { fontSize: '14px' }\n      },\n      xAxis: {\n        categories: Object.keys(categoryData.value),\n        labels: { style: { fontSize: '12px' } }\n      },\n      yAxis: {\n        title: {\n          text: '数量',\n          style: { fontSize: '12px' }\n        },\n        min: 0,\n        labels: { style: { fontSize: '12px' } }\n      },\n      series: [{\n        name: '图书数量',\n        data: Object.values(categoryData.value),\n        colorByPoint: true,\n        colors: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272']\n      }],\n      plotOptions: {\n        column: {\n          dataLabels: {\n            enabled: true,\n            style: { fontSize: '11px' }\n          },\n          borderRadius: 2\n        }\n      },\n      legend: { enabled: false },\n      tooltip: {\n        pointFormat: '<b>{point.y}</b> 本图书'\n      }\n    }))\n\n    // 系统状态仪表盘配置\n    const gaugeChartOptions = computed(() => {\n      const utilizationRate = Math.round((activeBorrowings.value / books.value.length) * 100)\n\n      return {\n        ...getBaseOptions(),\n        chart: {\n          type: 'solidgauge',\n          height: 250\n        },\n        title: {\n          text: '系统使用率',\n          style: { fontSize: '14px' }\n        },\n        pane: {\n          center: ['50%', '75%'],\n          size: '100%',\n          startAngle: -90,\n          endAngle: 90,\n          background: {\n            backgroundColor: '#EEE',\n            innerRadius: '60%',\n            outerRadius: '100%',\n            shape: 'arc'\n          }\n        },\n        yAxis: {\n          min: 0,\n          max: 100,\n          stops: [\n            [0.1, '#55BF3B'],\n            [0.5, '#DDDF0D'],\n            [0.9, '#DF5353']\n          ],\n          lineWidth: 0,\n          tickWidth: 0,\n          minorTickInterval: null,\n          tickAmount: 2,\n          title: {\n            y: -50,\n            text: '使用率 (%)',\n            style: { fontSize: '12px' }\n          },\n          labels: {\n            y: 16,\n            style: { fontSize: '12px' }\n          }\n        },\n        series: [{\n          name: '使用率',\n          data: [utilizationRate],\n          dataLabels: {\n            format: '<div style=\"text-align:center\"><span style=\"font-size:20px\">{y}</span><br/><span style=\"font-size:10px;opacity:0.4\">%</span></div>'\n          }\n        }],\n        plotOptions: {\n          solidgauge: {\n            dataLabels: {\n              y: 5,\n              borderWidth: 0,\n              useHTML: true\n            }\n          }\n        }\n      }\n    })\n\n    // 跳转到Highcharts演示页面\n    const goToHighchartsDemo = () => {\n      router.push('/admin/highcharts')\n    }\n\n    return {\n      books,\n      users,\n      borrowings,\n      activeBorrowings,\n      categoryData,\n      borrowingData,\n      categoryChartOptions,\n      gaugeChartOptions,\n      goToHighchartsDemo\n    }\n  }\n}\n</script>\n\n<style scoped>\n.home {\n  padding: 20px;\n}\n\n.dashboard {\n  margin-top: 20px;\n  margin-bottom: 20px;\n}\n\n.card-content {\n  text-align: center;\n  font-size: 24px;\n}\n\n.charts-row {\n  margin-top: 20px;\n}\n\n.chart-container {\n  height: 280px;\n  padding: 10px;\n  position: relative;\n}\n\n.chart {\n  height: 100%;\n  width: 100%;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-weight: bold;\n  color: #303133;\n}\n\n.features-showcase {\n  padding: 20px 0;\n}\n\n.feature-item {\n  text-align: center;\n  padding: 20px;\n  border-radius: 8px;\n  background: #f8f9fa;\n  height: 100%;\n  transition: all 0.3s ease;\n}\n\n.feature-item:hover {\n  background: #e9ecef;\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n\n.feature-icon {\n  font-size: 32px;\n  color: #409EFF;\n  margin-bottom: 10px;\n}\n\n.feature-item h4 {\n  margin: 10px 0;\n  color: #303133;\n  font-size: 16px;\n}\n\n.feature-item p {\n  color: #606266;\n  font-size: 14px;\n  line-height: 1.5;\n  margin: 0;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .chart-container {\n    height: 200px;\n  }\n\n  .features-showcase {\n    padding: 10px 0;\n  }\n\n  .feature-item {\n    padding: 15px;\n    margin-bottom: 10px;\n  }\n\n  .feature-icon {\n    font-size: 24px;\n  }\n\n  .feature-item h4 {\n    font-size: 14px;\n  }\n\n  .feature-item p {\n    font-size: 12px;\n  }\n}\n</style>\n", "import { render } from \"./Home.vue?vue&type=template&id=382f89fb&scoped=true\"\nimport script from \"./Home.vue?vue&type=script&lang=js\"\nexport * from \"./Home.vue?vue&type=script&lang=js\"\n\nimport \"./Home.vue?vue&type=style&index=0&id=382f89fb&scoped=true&lang=css\"\n\nimport exportComponent from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\软件架构大作业\\\\homework\\\\node_modules\\\\vue-loader-v16\\\\dist\\\\exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-382f89fb\"]])\n\nexport default __exports__", "export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader-v16/dist/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader-v16/dist/index.js??ref--1-1!./HighchartsWrapper.vue?vue&type=style&index=0&id=f326fe4a&scoped=true&lang=css\"", "export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader-v16/dist/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader-v16/dist/index.js??ref--1-1!./Home.vue?vue&type=style&index=0&id=382f89fb&scoped=true&lang=css\""], "sourceRoot": ""}