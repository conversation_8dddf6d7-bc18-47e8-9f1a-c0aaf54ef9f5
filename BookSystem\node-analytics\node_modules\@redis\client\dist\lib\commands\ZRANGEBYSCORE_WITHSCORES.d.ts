import { RedisCommandArgument, RedisCommandArguments } from '.';
import { ZRangeByScoreOptions } from './ZRANGEBYSCORE';
export { FIRST_KEY_INDEX, IS_READ_ONLY } from './ZRANGEBYSCORE';
export declare function transformArguments(key: RedisCommandArgument, min: string | number, max: string | number, options?: ZRangeByScoreOptions): RedisCommandArguments;
export { transformSortedSetWithScoresReply as transformReply } from './generic-transformers';
