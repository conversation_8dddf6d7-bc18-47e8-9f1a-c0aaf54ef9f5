# 图书管理系统 - Highcharts数据可视化演示

这是一个基于Vue 3的图书管理系统，重点展示了Highcharts在数据可视化方面的强大功能和用途。

## 🚀 项目特色

### Highcharts功能展示
本项目全面展示了Highcharts在实际业务场景中的应用，包括：

#### 📊 多种图表类型
- **柱状图** - 图书分类统计，清晰展示各类别图书数量
- **饼图** - 借阅分布统计，直观显示借阅比例
- **折线图** - 借阅趋势分析，展示时间序列数据
- **面积图** - 库存变化趋势，显示数据的累积效果
- **仪表盘** - 系统状态监控，实时显示使用率
- **热力图** - 借阅热度分析，多维度数据展示
- **3D图表** - 立体数据展示，增强视觉效果
- **组合图表** - 多维度数据对比，柱状图+折线图组合

#### 🎯 核心功能特性
1. **交互功能**
   - 鼠标悬停显示详细信息
   - 点击图例隐藏/显示数据系列
   - 缩放和平移功能
   - 数据钻取功能
   - 动态数据更新

2. **导出功能**
   - 导出PNG图片
   - 导出JPEG图片
   - 导出PDF文档
   - 导出SVG矢量图
   - 打印图表

3. **响应式设计**
   - 自适应屏幕尺寸
   - 移动设备优化
   - 触摸手势支持
   - 高分辨率显示
   - 主题定制

#### 🎨 页面展示
- **首页** - 展示系统概览和Highcharts基础图表
- **数据统计页** - 完整的Highcharts图表集合
- **Highcharts演示页** - 专门的功能展示和图表类型切换

## 🛠 技术栈

- **前端框架**: Vue 3 + Composition API
- **UI组件库**: Element Plus
- **图表库**: Highcharts + highcharts-vue
- **状态管理**: Vuex 4
- **路由管理**: Vue Router 4
- **构建工具**: Vue CLI

## 📦 安装和运行

### 项目安装
```bash
npm install
```

### 开发环境运行
```bash
npm run serve
```

### 生产环境构建
```bash
npm run build
```

### 代码检查和修复
```bash
npm run lint
```

## 🎯 Highcharts用途体现

### 1. 业务数据可视化
- 图书分类统计：使用柱状图清晰展示各类别图书数量
- 借阅分布分析：使用饼图直观显示借阅比例
- 趋势分析：使用折线图展示借阅趋势变化

### 2. 系统监控仪表盘
- 使用仪表盘图表实时显示系统使用率
- 动态更新数据，提供实时监控能力
- 颜色分级显示，直观反映系统状态

### 3. 多维度数据分析
- 热力图展示借阅热度分析
- 组合图表对比库存与借阅数据
- 3D图表提供立体数据展示

### 4. 专业级图表功能
- 完整的导出功能支持
- 响应式设计适配各种设备
- 丰富的交互功能增强用户体验

## 🌟 项目亮点

1. **实际业务场景**: 结合图书管理系统的真实需求，展示Highcharts的实用性
2. **功能全面**: 涵盖Highcharts的主要图表类型和功能特性
3. **用户体验**: 注重交互设计和响应式布局
4. **代码质量**: 使用Vue 3最新特性，代码结构清晰
5. **可扩展性**: 模块化设计，易于扩展和维护

## 📱 访问方式

1. 启动项目后，使用管理员账号登录：
   - 用户名: `admin`
   - 密码: `admin123`

2. 访问以下页面查看Highcharts功能：
   - 首页：基础图表展示
   - 数据统计：完整图表集合
   - Highcharts演示：专门的功能展示页面

## 🖥️ Electron桌面应用

### 快速启动
1. **Windows批处理文件**
   ```
   双击运行 run-app.bat
   ```

2. **PowerShell脚本**
   ```
   右键 run-app.ps1 -> 使用PowerShell运行
   ```

### 手动运行
```bash
# 开发模式（需要先启动Vue开发服务器）
npm run electron-dev

# 生产模式（自动构建并运行）
npm run electron-prod
```

### 系统要求
- Node.js 14.0+
- npm 6.0+
- Windows 10+ / macOS 10.14+ / Linux

## 🔧 自定义配置
参考 [Vue CLI配置文档](https://cli.vuejs.org/config/)

## 📄 许可证
本项目仅用于学习和演示目的。
