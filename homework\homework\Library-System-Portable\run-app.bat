@echo off
echo 启动杭师大图书管理系统...
echo.

REM 检查Node.js是否安装
node --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未检测到Node.js，请先安装Node.js
    pause
    exit /b 1
)

REM 检查npm是否可用
npm --version >nul 2>&1
if errorlevel 1 (
    echo 错误：npm不可用，请检查Node.js安装
    pause
    exit /b 1
)

REM 切换到应用目录
cd /d "%~dp0"

REM 检查依赖是否安装
if not exist "node_modules" (
    echo 正在安装依赖...
    npm install
    if errorlevel 1 (
        echo 依赖安装失败，请检查网络连接
        pause
        exit /b 1
    )
)

REM 构建应用
echo 正在构建应用...
npm run build
if errorlevel 1 (
    echo 构建失败
    pause
    exit /b 1
)

REM 启动Electron应用
echo 启动应用...
npm run electron-prod

pause
